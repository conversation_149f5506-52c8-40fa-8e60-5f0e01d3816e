<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="WizardSettings">
    <option name="children">
      <map>
        <entry key="imageWizard">
          <value>
            <PersistentState>
              <option name="children">
                <map>
                  <entry key="imageAssetPanel">
                    <value>
                      <PersistentState>
                        <option name="children">
                          <map>
                            <entry key="actionbar">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="clipArt">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                                <entry key="imagePath" value="/private/var/folders/6q/_0nt7ry13c5083cspd1rl5q00000gn/T/ic_android_black_24dp.xml" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="text">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="textAsset">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                            <entry key="launcher">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="backgroundImage">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="imagePath" value="$USER_HOME$/Downloads/安卓icon/bg/Artboard 7 Copy 11.png" />
                                                <entry key="trimmed" value="true" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundClipArt">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="imagePath" value="/private/var/folders/6q/_0nt7ry13c5083cspd1rl5q00000gn/T/ic_android_black_24dp.xml" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundImage">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                                <entry key="imagePath" value="$USER_HOME$/Downloads/安卓icon/foreground/Artboard 7 Copy 11.png" />
                                                <entry key="trimmed" value="true" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundText">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundTextAsset">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                  <option name="values">
                                    <map>
                                      <entry key="backgroundColor" value="1c1c1d" />
                                      <entry key="previewDensity" value="mdpi" />
                                      <entry key="showSafeZone" value="false" />
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                            <entry key="launcherLegacy">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="clipArt">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                                <entry key="imagePath" value="/private/var/folders/6q/_0nt7ry13c5083cspd1rl5q00000gn/T/ic_android_black_24dp.xml" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="image">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                                <entry key="imagePath" value="$USER_HOME$/Downloads/<EMAIL>" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="text">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="textAsset">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                  <option name="values">
                                    <map>
                                      <entry key="assetType" value="IMAGE" />
                                      <entry key="imageAsset" value="$USER_HOME$/Downloads/<EMAIL>" />
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                            <entry key="notification">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="clipArt">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                                <entry key="imagePath" value="/private/var/folders/6q/_0nt7ry13c5083cspd1rl5q00000gn/T/ic_android_black_24dp.xml" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="text">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="textAsset">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                            <entry key="tvBanner">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="foregroundText">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                            <entry key="tvChannel">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="foregroundClipArt">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="imagePath" value="/private/var/folders/6q/_0nt7ry13c5083cspd1rl5q00000gn/T/ic_android_black_24dp.xml" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundImage">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundText">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundTextAsset">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                  <option name="values">
                                    <map>
                                      <entry key="previewDensity" value="mdpi" />
                                      <entry key="showSafeZone" value="false" />
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                          </map>
                        </option>
                      </PersistentState>
                    </value>
                  </entry>
                </map>
              </option>
            </PersistentState>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>