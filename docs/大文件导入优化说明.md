# 大文件CSV导入优化说明

## 概述
针对上百万条GPS数据的导入场景，我们实现了流式读取和批量处理的优化方案，确保内存使用可控，性能稳定。

## 核心优化特性

### 1. 流式文件读取
- **避免内存暴增**: 不再一次性读取整个文件到内存
- **逐行处理**: 使用BufferedReader逐行读取和处理数据
- **实时处理**: 边读取边解析边写入数据库

### 2. 批量数据库写入
- **批次大小**: 每10,000条数据为一批进行数据库写入
- **事务优化**: 每批使用单个Realm事务，提高写入性能
- **内存释放**: 每批处理完成后立即清空内存中的数据

### 3. 内存管理优化
- **时间戳集合管理**: 限制重复检查集合大小，避免无限增长
- **垃圾回收建议**: 每10批处理后建议系统进行垃圾回收
- **内存监控**: 记录处理前后的内存使用情况
- **协程让步**: 定期让出执行权，避免阻塞UI线程

### 4. 详细进度反馈
- **实时进度**: 显示处理进度百分比
- **统计信息**: 实时更新成功、错误、重复数据统计
- **批次信息**: 显示当前处理批次和总批次数
- **内存状态**: 监控内存使用情况

## 技术实现

### 流式解析器 (CsvDataParser)

#### 新增方法
```kotlin
suspend fun parseCSVFileStreaming(
    uri: Uri, 
    progressCallback: ProgressCallback? = null
): ParseResult
```

#### 进度回调接口
```kotlin
interface ProgressCallback {
    suspend fun onProgress(
        processedRows: Int,
        totalRows: Int,
        successRows: Int,
        errorRows: Int,
        duplicateRows: Int,
        currentBatch: Int,
        totalBatches: Int
    )
    
    suspend fun onBatchCompleted(batchNumber: Int, batchSize: Int)
}
```

### 批量写入优化 (RealmModelManager)

#### 高性能批量写入
```kotlin
suspend fun writeBatchToRealmOptimized(models: List<StepDataRealmModel>)
```

- 使用单个Realm事务写入整批数据
- 错误容忍：单条数据错误不影响整批处理
- 性能优化：减少事务开销

### 内存管理策略

#### 1. 批次处理
- **批次大小**: 10,000条数据/批
- **内存清理**: 每批处理完成后立即清空
- **时间戳管理**: 限制重复检查集合大小

#### 2. 垃圾回收
- **建议频率**: 每10批处理后
- **非强制**: 仅建议系统进行GC，不强制执行
- **内存监控**: 记录内存使用变化

#### 3. 协程优化
- **定期让步**: 使用`kotlinx.coroutines.yield()`
- **UI响应**: 确保UI线程不被阻塞
- **异步处理**: 所有文件操作都在后台线程

## 性能指标

### 内存使用
- **峰值内存**: 约为批次大小的2-3倍
- **稳定内存**: 处理过程中内存使用相对稳定
- **内存增长**: 整个导入过程内存增长可控

### 处理速度
- **小文件**: 与原方案性能相当
- **大文件**: 显著优于一次性加载方案
- **响应性**: UI保持响应，用户体验良好

### 数据完整性
- **重复检查**: 保持原有的重复数据检测功能
- **错误处理**: 单行错误不影响整体导入
- **事务安全**: 批量写入保证数据一致性

## 使用场景

### 适用情况
- ✅ 大文件导入（>100万条数据）
- ✅ 内存受限设备
- ✅ 需要进度反馈的长时间操作
- ✅ 对UI响应性有要求的场景

### 兼容性
- ✅ 保持原有API兼容性
- ✅ 支持原有的小文件导入方式
- ✅ 错误处理机制不变
- ✅ 数据验证逻辑不变

## 测试建议

### 1. 小文件测试
- 使用原有的demo数据文件测试基本功能
- 验证数据完整性和准确性

### 2. 大文件测试
- 创建包含100万+条数据的CSV文件
- 监控内存使用情况
- 验证进度反馈的准确性

### 3. 压力测试
- 测试极大文件（500万+条数据）
- 验证内存使用是否可控
- 测试长时间运行的稳定性

### 4. 用户体验测试
- 验证UI响应性
- 测试进度显示的准确性
- 验证错误处理的用户友好性

## 配置参数

### 可调整参数
```kotlin
// 批量处理大小（可根据设备性能调整）
private const val BATCH_SIZE = 10000

// 垃圾回收建议频率（每N批）
private const val GC_SUGGESTION_INTERVAL = 10

// 时间戳集合最大大小
private const val MAX_TIMESTAMP_SET_SIZE = BATCH_SIZE * 2
```

### 性能调优建议
- **低内存设备**: 减小BATCH_SIZE到5000
- **高性能设备**: 增大BATCH_SIZE到20000
- **SSD存储**: 可以增大批次大小
- **机械硬盘**: 保持默认批次大小

## 监控和日志

### 内存监控
- 记录处理开始时的内存使用
- 记录处理结束时的内存使用
- 计算内存增长量

### 性能日志
- 批次处理时间
- 数据库写入性能
- 文件读取速度

### 错误日志
- 数据解析错误详情
- 数据库写入错误
- 内存不足警告
