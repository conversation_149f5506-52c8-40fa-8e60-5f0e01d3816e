# FootPrint - 一生足迹 Android版

## 项目简介
这是一个基于移动设备GPS数据，记录用户轨迹的APP，支持前后台自动记录，轨迹数据查看，以及丰富可视化效果的安卓APP。

## 最新功能更新

### 详细数据面板 (DetailDataPanel)
新增了与iOS版本一致的详细数据面板功能，支持：

#### 功能特性
- **层级数据浏览**: 按年-月-日三级结构浏览GPS轨迹数据
- **智能默认选择**: 自动显示最后一天的数据作为默认
- **实时数据加载**: 点击年份自动加载该年第一天有数据的日期
- **详细数据点信息**: 显示每个GPS点的时间、坐标、速度、海拔、精度等信息

#### 界面设计
- **合理屏幕分配**: 地图区域占60%，详细面板占40%，确保地图轨迹可见
- **优化三列布局**: 年份(15%) | 日期(20%) | 数据点详情(65%)
- **紧凑顶部工具栏**: 导出、编辑、关闭功能按钮，降低高度
- **底部工具栏**: 全选、预删除功能按钮
- **异步数据加载**: 日期列轨迹点数量异步加载，避免界面卡顿
- **深色主题**: 黑色背景配合红色强调色，与应用整体风格一致
- **渐变背景**: 不同列使用不同深度的灰色背景便于区分
- **简化数据显示**: 每个数据点仅显示两行关键信息

#### 交互逻辑
1. 点击地图页面右侧控制栏的"详细"按钮
2. 面板从底部弹出，默认显示最后一天的数据
3. 点击年份 → 自动加载该年所有有数据的日期
4. 点击日期 → 显示该日所有GPS数据点，**地图同步显示该天轨迹**
5. 点击数据点 → **地图显示选中点位置，保持轨迹线不消失**
6. 数据点按时间升序排列，显示两行关键信息：
   - 第一行：时间(坐标) - 如 "00:01:07 (103.9114,30.5904)"
   - 第二行：速度、海拔、定位精度 - 如 "速度:1.00 海拔:512.64 定位精度:11.43"
7. 顶部工具栏提供导出、编辑、关闭功能
8. 底部工具栏提供全选、预删除功能

#### 地图联动功能
- **轨迹同步显示**: 选择某天数据时，地图自动显示该天的完整轨迹（蓝色线条）
- **智能轨迹切换**: 显示详细轨迹时自动隐藏主轨迹，避免混合显示
- **数据点定位**: 点击数据点时，地图中心移动到该点位置并显示黄色标记
- **动态视角调整**: 根据详细面板实际高度动态计算地图边距（面板高度+100dp），适配不同设备
- **状态管理**: 关闭详细面板时自动恢复主轨迹显示

## 技术栈
- **主要编程语言**: Kotlin
- **UI框架**: Jetpack Compose
- **地图模块**: MapBox
- **数据存储**: Realm数据库
- **架构模式**: MVVM + 单例模式

## 核心组件

### 数据管理
- `RealmModelManager`: 数据库管理，新增了按年月日查询的方法
- `StepDataRealmModel`: GPS数据模型
- `AppPrefs`: 应用偏好设置

### UI组件
- `MainMapActivity`: 主地图界面
- `DetailDataPanel`: 详细数据面板（新增）
- `ConfigPanel`: 配置面板
- `MapControlButtons`: 地图控制按钮

### 位置服务
- `LocationManager`: 位置管理服务
- `LocationService`: 后台位置服务
- `LocationDataRecorder`: 位置数据记录器

## 新增的数据库查询方法

```kotlin
// 获取所有有数据的年份
suspend fun getAvailableYears(): List<Int>

// 获取指定年份有数据的月份
suspend fun getAvailableMonthsInYear(year: Int): List<Int>

// 获取指定年月有数据的日期
suspend fun getAvailableDaysInMonth(year: Int, month: Int): List<Int>

// 获取指定日期的所有数据点
suspend fun getDataPointsForDay(year: Int, month: Int, day: Int): List<StepDataRealmModel>

// 获取指定年份第一天有数据的日期
suspend fun getFirstDataDayInYear(year: Int): Triple<Int, Int, Int>?

// 获取最后一天有数据的日期
suspend fun getLastDataDay(): Triple<Int, Int, Int>?

// 获取指定日期的轨迹点数量（用于异步加载）
suspend fun getDataPointCountForDay(year: Int, month: Int, day: Int): Int

// 支持自定义边距的轨迹缩放函数
fun fitTrackAndAnimateWithPadding(
    mapView: MapView,
    mapViewportState: MapViewportState,
    trackPoints: List<Point>,
    bottomPadding: Double = 400.0  // 动态计算：面板高度+100
)
```

## 使用说明

1. **启动应用**: 应用会自动请求位置权限并开始记录GPS轨迹
2. **查看轨迹**: 主界面显示当前轨迹，可通过底部控制栏切换不同时间段
3. **详细数据**: 点击右侧"详细"按钮查看所有历史数据点
4. **配置设置**: 点击"配置"按钮调整地图样式、轨迹颜色等设置
5. **运行模式**: 点击顶部标题可切换普通/耗电/省电三种模式
