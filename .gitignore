# Android Studio
*.iml
.gradle
/local.properties
/.idea/workspace.xml
/.idea/tasks.xml
/.idea/dictionaries
/.idea/libraries
.DS_Store

# Android
/build
/captures
.externalNativeBuild
.cxx
local.properties

# Gradle
.gradle/
/build/

# Local configuration file (sdk path, etc)
local.properties

# Proguard folder generated by Eclipse
proguard/

# Log Files
*.log

# Android Studio Navigation editor temp files
.navigation/

# Android Studio captures folder
captures/

# IntelliJ
*.iws
*.ipr
*.iws

# Keystore files
*.jks

# External native build folder generated in Android Studio 2.2 and later
.externalNativeBuild/

# NDK
obj/

# Google Services (e.g. APIs or Firebase)
google-services.json

# Android Studio 3.1+ serialized cache file
.idea/codeStyles/Project.xml
.idea/codeStyles/codeStyleConfig.xml

# Uncomment the following line if you want to ignore the Android Studio 3.5+ build output
# .idea/assetWizardSettings.xml

# Uncomment the following line if you want to ignore the Android Studio 4.0+ build output
# .idea/assetWizardSettings.xml

# Uncomment the following line if you want to ignore the Android Studio 4.1+ build output
# .idea/assetWizardSettings.xml
