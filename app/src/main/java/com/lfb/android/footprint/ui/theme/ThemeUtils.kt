package com.lfb.android.footprint.ui.theme

import android.content.Context
import com.lfb.android.footprint.R
import com.lfb.android.footprint.prefs.AppPrefs

/**
 * 主题工具类
 * 用于在Activity启动时根据用户配置设置正确的主题，避免白屏闪烁
 */
object ThemeUtils {
    
    /**
     * 根据地图显示类型获取对应的Activity主题资源ID
     * @param context Context对象，用于访问AppPrefs
     * @return 主题资源ID
     */
    fun getActivityThemeForMapDisplayType(context: Context): Int {
        return try {
            // 确保AppPrefs已初始化
            val mapDisplayType = AppPrefs.sharedInstance.mapDisplayType
            getActivityThemeForMapDisplayType(mapDisplayType)
        } catch (e: Exception) {
            // 如果AppPrefs未初始化或出现其他错误，使用默认深色主题
            R.style.Theme_FootPrint
        }
    }
    
    /**
     * 根据地图显示类型获取对应的Activity主题资源ID
     * @param mapDisplayType 地图显示类型
     * @return 主题资源ID
     */
    fun getActivityThemeForMapDisplayType(mapDisplayType: Int): Int {
        return when (mapDisplayType) {
            0, 5 -> R.style.Theme_FootPrint        // 通用地图和卫星图 - 深色主题
            else -> R.style.Theme_FootPrint_Light  // 其他地图样式 - 浅色主题
        }
    }
    
    /**
     * 检查指定的地图显示类型是否使用深色主题
     * @param mapDisplayType 地图显示类型
     * @return true表示深色主题，false表示浅色主题
     */
    fun isDarkThemeForMapDisplayType(mapDisplayType: Int): Boolean {
        return when (mapDisplayType) {
            0, 5 -> true   // 通用地图和卫星图
            else -> false  // 其他地图样式
        }
    }
}
