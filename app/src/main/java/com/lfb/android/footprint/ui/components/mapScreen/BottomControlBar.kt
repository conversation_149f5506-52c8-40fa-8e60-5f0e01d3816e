package com.lfb.android.footprint.ui.components.mapScreen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.shadow
import com.lfb.android.footprint.ui.theme.mainRedColor
import com.lfb.android.footprint.ui.theme.rememberMapThemeConfig
import com.lfb.android.footprint.prefs.AppPrefs

@Composable
fun BottomControlBar(
    onFilterSelected: (Int) -> Unit,
    onLeftButtonClick: () -> Unit,
    onRightButtonClick: () -> Unit,
    leftIconResId: Int,
    rightIconResId: Int,
    modifier: Modifier = Modifier
) {
    val themeConfig = rememberMapThemeConfig()
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(100.dp)
            .shadow(
                elevation = 4.dp,
                shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
            )
            .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
            .background(
                color = themeConfig.backgroundColor,
                shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
            )
    ) {

        // TrackFilter in the center

        TimePickerView(
            modifier = Modifier.align(Alignment.Center)
                .offset(x = 4.dp)
                .width(180.dp)
//                .background(Color.Green)
            ,
            onItemSelected = onFilterSelected
        )


        // Left Button
        ControlButton(
            onClick = onLeftButtonClick,
            iconResId = leftIconResId,
            contentDescription = "Left Button",
            modifier = Modifier
                .align(Alignment.CenterStart)
                .padding(start = 16.dp)
        )



        // Right Button
        ControlButton(
            onClick = onRightButtonClick,
            iconResId = rightIconResId,
            contentDescription = "Right Button",
            modifier = Modifier
                .align(Alignment.CenterEnd)
                .padding(end = 16.dp)
        )
    }
}

@Composable
private fun ControlButton(
    onClick: () -> Unit,
    iconResId: Int,
    contentDescription: String,
    modifier: Modifier = Modifier
) {
    var isPressed by remember { mutableStateOf(false) }

    Box(
        modifier = modifier
            .size(44.dp)
            .alpha(if (isPressed) 0.5f else 1f) // 点击时透明度变化
            .pointerInput(Unit) {
                detectTapGestures(
                    onPress = {
                        isPressed = true
                        tryAwaitRelease()
                        isPressed = false
                    },
                    onTap = { onClick() }
                )
            },
        contentAlignment = Alignment.Center
    ) {
        Image(
            painter = painterResource(id = iconResId),
            contentDescription = contentDescription,
            modifier = Modifier.size(40.dp),
            colorFilter = ColorFilter.tint(mainRedColor)
        )
    }
}