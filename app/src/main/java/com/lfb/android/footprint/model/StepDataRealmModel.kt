package com.lfb.android.footprint.model

import io.realm.kotlin.types.RealmObject
import io.realm.kotlin.types.annotations.Index
import io.realm.kotlin.types.annotations.PrimaryKey
import org.mongodb.kbson.ObjectId

class StepDataRealmModel : RealmObject {
    @PrimaryKey
    var dataTime: Long = 0L // 时间
    var longitude: Double = 0.0 // 经度
    var latitude: Double = 0.0  // 纬度
    @Index // 为年份字段添加索引，提高查询性能
    var year: Int = 0       // 年份
    var day: Int = 0        // 日
    var dayOfThisyear: Int = 0 // 那一年的第几天
    var heading: Double = 0.0   // 朝向
    var altitude: Double = 0.0  // 海拔
    var hAccuracy: Double = 0.0 // 水平精度
    var vAccuracy: Double = 0.0 // 垂直精度
    var speed: Double = 0.0     // 移动速度
    var distance: Double = 0.0  // 距离上个点的距离
}