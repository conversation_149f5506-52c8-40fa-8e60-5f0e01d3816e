package com.lfb.android.footprint.ui.components.mapScreen

import android.location.Location
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.mapbox.geojson.Point
import com.lfb.android.footprint.R
import com.lfb.android.footprint.location.LocationManager
import com.mapbox.maps.extension.compose.animation.viewport.MapViewportState

@Composable
fun MapOverlayComponents(
    currentLocation: Location?,
    runningModel: Int,
    trackPoints: MutableList<Point>,
    selectedFilter: Int,
    drawPointAnimationType: DrawPointAnimationType,
    mapViewportState: MapViewportState,
    locationManager: LocationManager,
    onRecordModeClick: () -> Unit,
    onFilterSelected: (Int) -> Unit,
    onLocationClick: () -> Unit,
    onConfigClick: () -> Unit,
    onDetailClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // App Header
    AppHeader(
        gpsSignal = currentLocation?.accuracy?.toInt() ?: 0,
        runningModel = runningModel,
        modifier = modifier
            .padding(start = 16.dp, top = 38.dp)
            .clickable { onRecordModeClick() }
    )

    // 当前状态信息卡片
    MapCurrentStateInfoCard(
        altitude = currentLocation?.altitude?.toFloat() ?: 0f,
        speed = currentLocation?.speed ?: 0f,
        modifier = modifier
            .padding(start = 16.dp, top = 88.dp)
    )

    // 数据信息卡片
    MapDataInfoCard(
        dataCount = trackPoints.count(),
        dataDistance = calculateTotalDistance(trackPoints).toDouble(),
        selectedIndex = selectedFilter,
        modifier = modifier
            .padding(end = 12.dp, bottom = 88.dp)
    )

    // 地图控制按钮
    MapControlButtons(
        onLocationClick = onLocationClick,
        onConfigClick = onConfigClick,
        onDetailClick = onDetailClick,
        modifier = modifier
            .padding(end = 12.dp, bottom = 108.dp)
    )

    // 底部控制栏
    BottomControlBar(
        onFilterSelected = onFilterSelected,
        onLeftButtonClick = { },
        onRightButtonClick = { },
        leftIconResId = R.drawable.menu,
        rightIconResId = R.drawable.overview,
        modifier = modifier
    )
}

// 计算总距离的函数
fun calculateTotalDistance(points: List<Point>): Float {
    if (points.size < 2) return 0f
    
    var totalDistance = 0.0
    for (i in 1 until points.size) {
        val prevPoint = points[i - 1]
        val currentPoint = points[i]
        
        // 使用 Haversine 公式计算两点间距离
        val distance = haversineDistance(
            prevPoint.latitude(), prevPoint.longitude(),
            currentPoint.latitude(), currentPoint.longitude()
        )
        totalDistance += distance
    }
    
    return (totalDistance / 1000).toFloat() // 转换为公里
}

// Haversine 公式计算两点间距离（米）
private fun haversineDistance(lat1: Double, lon1: Double, lat2: Double, lon2: Double): Double {
    val R = 6371000.0 // 地球半径（米）
    val dLat = Math.toRadians(lat2 - lat1)
    val dLon = Math.toRadians(lon2 - lon1)
    val a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) *
            Math.sin(dLon / 2) * Math.sin(dLon / 2)
    val c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return R * c
}
