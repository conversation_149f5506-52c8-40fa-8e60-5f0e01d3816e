package com.lfb.android.footprint

import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import com.lfb.android.footprint.prefs.AppPrefs
import com.lfb.android.footprint.ui.components.guideScreen.GuideScreen
import com.lfb.android.footprint.ui.theme.ThemeUtils
import com.lfb.android.footprint.utils.PermissionManager

class GuideActivity : BaseActivity() {

    private lateinit var permissionManager: PermissionManager

    override fun onCreate(savedInstanceState: Bundle?) {
        // 根据用户的地图主题配置设置Activity主题
        setTheme(ThemeUtils.getActivityThemeForMapDisplayType(this))

        super.onCreate(savedInstanceState)

        // 初始化权限管理器
        permissionManager = PermissionManager(this)

        // 检查是否已显示过引导页
        if (AppPrefs.sharedInstance.hasShowGuidPage) {
            // 已显示过，直接跳转到主页面
            val intent = Intent(this, MainMapActivity::class.java)
            startActivity(intent)
            finish()
            return
        }

        setContent {
            GuideScreen(
                permissionManager = permissionManager,
                onFinishGuide = {
                    // 标记已显示过引导页
                    AppPrefs.sharedInstance.hasShowGuidPage = true

                    // 跳转到主页面
                    val intent = Intent(this@GuideActivity, MainMapActivity::class.java)
                    startActivity(intent)
                    finish()
                }
            )
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        permissionManager.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }
}
