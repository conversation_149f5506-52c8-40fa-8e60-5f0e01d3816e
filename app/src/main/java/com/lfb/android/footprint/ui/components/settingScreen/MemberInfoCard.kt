package com.lfb.android.footprint.ui.components.settingScreen

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.lfb.android.footprint.Manager.RealmModelManager
import com.lfb.android.footprint.ui.theme.mainRedColor
import kotlinx.coroutines.launch
import java.text.NumberFormat
import java.util.Locale

@Composable
internal fun MemberInfoCard() {
    val coroutineScope = rememberCoroutineScope()
    var totalTrackPoints by remember { mutableStateOf(0L) }
    var totalDistance by remember { mutableStateOf(0.0) }
    var totalDays by remember { mutableStateOf(0L) }
    var isLoading by remember { mutableStateOf(true) }

    // 从数据库加载统计数据（两阶段缓存策略）
    LaunchedEffect(Unit) {
        coroutineScope.launch {
            try {
                val realmManager = RealmModelManager.getInstance()

                // 第一阶段：立即获取缓存数据
                val (cachedTrackPoints, cachedDistance, cachedDays) = realmManager.getLifetimeDataStats { updatedStats ->
                    // 第二阶段：数据更新后的回调
                    totalTrackPoints = updatedStats.first
                    totalDistance = updatedStats.second
                    totalDays = updatedStats.third
                    println("MemberInfoCard: Stats updated from database - TrackPoints: ${updatedStats.first}, Distance: ${updatedStats.second}, Days: ${updatedStats.third}")
                }

                // 立即显示缓存数据
                totalTrackPoints = cachedTrackPoints
                totalDistance = cachedDistance
                totalDays = cachedDays
                isLoading = false

                println("MemberInfoCard: Initial stats from cache - TrackPoints: $cachedTrackPoints, Distance: $cachedDistance, Days: $cachedDays")
            } catch (e: Exception) {
                println("Failed to load lifetime stats: ${e.message}")
                isLoading = false
            }
        }
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(88.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = mainRedColor)
    ) {
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            // 会员中心按钮
            Text(
                text = "会员中心",
                color = Color.White,
                fontSize = 13.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(12.dp)
                    .clickable { /* TODO: 会员中心点击事件 */ }
            )

            // 主要内容
            Column(
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .padding(start = 16.dp)
            ) {
                Text(
                    text = "足迹 Plus 会员专属档案",
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(6.dp))

                // 统计数据行
                Row(
                    horizontalArrangement = Arrangement.spacedBy(20.dp)
                ) {
                    if (isLoading) {
                        StatItem(label = "累计轨迹", value = "--", unit = "")
                        StatItem(label = "累计时长", value = "--", unit = "天")
                        StatItem(label = "累计距离", value = "--", unit = "km")
                    } else {
                        // 格式化轨迹点数量（添加千分位分隔符）
                        val formattedTrackPoints = NumberFormat.getNumberInstance(Locale.getDefault()).format(totalTrackPoints)

                        // 格式化距离（保留两位小数）
                        val formattedDistance = String.format("%.2f", totalDistance / 1000)

                        StatItem(label = "累计轨迹", value = formattedTrackPoints, unit = "")
                        StatItem(label = "累计时长", value = totalDays.toString(), unit = "天")
                        StatItem(label = "累计距离", value = formattedDistance, unit = "km")
                    }
                }
            }
        }
    }
}

@Composable
private fun StatItem(label: String, value: String, unit: String) {
    Column {
        Text(
            text = label,
            color = Color.White.copy(alpha = 0.7f),
            fontSize = 9.sp
        )
        Text(
            text = "$value $unit",
            color = Color.White,
            fontSize = 11.sp,
            fontWeight = FontWeight.Medium
        )
    }
}
