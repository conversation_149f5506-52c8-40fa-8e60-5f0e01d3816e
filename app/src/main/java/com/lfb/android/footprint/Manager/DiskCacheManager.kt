package com.lfb.android.footprint.Manager

import android.content.Context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.builtins.serializer
import java.io.File
import java.io.IOException
// 序列化数据类型定义
@Serializable
data class SerializableTriple(val first: Int, val second: Int, val third: Int)

@Serializable
data class SerializablePair(val first: Long, val second: Double)


/**
 * 磁盘缓存管理器
 * 提供持久化缓存功能，用于在应用重启后保持缓存数据
 */
class DiskCacheManager private constructor(private val context: Context) {

    companion object {
        @Volatile
        private var instance: DiskCacheManager? = null

        fun getInstance(context: Context): DiskCacheManager {
            return instance ?: synchronized(this) {
                instance ?: DiskCacheManager(context.applicationContext).also { instance = it }
            }
        }

        // 磁盘缓存目录名
        private const val CACHE_DIR_NAME = "data_cache"
        
        // 缓存文件扩展名
        private const val CACHE_FILE_EXTENSION = ".cache"
        
        // 缓存元数据文件名
        private const val METADATA_FILE_NAME = "cache_metadata.json"
        
        // 最大磁盘缓存大小 (50MB)
        private const val MAX_DISK_CACHE_SIZE = 50 * 1024 * 1024L
        
        // 缓存清理阈值 (当缓存大小超过最大值的80%时开始清理)
        private const val CLEANUP_THRESHOLD = 0.8
    }

    // 缓存项元数据
    @Serializable
    private data class CacheMetadata(
        val key: String,
        val timestamp: Long,
        val ttl: Long,
        val lastAccessed: Long,
        val fileSize: Long
    ) {
        fun isExpired(): Boolean {
            return System.currentTimeMillis() - timestamp > ttl
        }

        fun isValid(): Boolean {
            return !isExpired()
        }
    }

    // 所有缓存项的元数据
    @Serializable
    private data class CacheMetadataCollection(
        val items: MutableMap<String, CacheMetadata> = mutableMapOf()
    )

    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
    }

    private val mutex = Mutex()
    private val cacheDir: File by lazy {
        File(context.cacheDir, CACHE_DIR_NAME).apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }

    private val metadataFile: File by lazy {
        File(cacheDir, METADATA_FILE_NAME)
    }

    private var metadata: CacheMetadataCollection = CacheMetadataCollection()

    init {
        // 初始化时加载元数据
        loadMetadata()
    }

    /**
     * 存储数据到磁盘缓存 - 通用方法
     */
    suspend fun put(key: String, data: Any, ttl: Long) = withContext(Dispatchers.IO) {
        // 根据数据类型调用相应的存储方法
        when (data) {
            is List<*> -> {
                @Suppress("UNCHECKED_CAST")
                when {
                    data.all { it is Int } -> putIntList(key, data as List<Int>, ttl)
                    else -> putStringList(key, data.map { it.toString() }, ttl)
                }
            }
            is Triple<*, *, *> -> {
                if (data.first is Int && data.second is Int && data.third is Int) {
                    @Suppress("UNCHECKED_CAST")
                    putTriple(key, data as Triple<Int, Int, Int>, ttl)
                }
            }
            is Pair<*, *> -> {
                if (data.first is Long && data.second is Double) {
                    @Suppress("UNCHECKED_CAST")
                    putStatsPair(key, data as Pair<Long, Double>, ttl)
                }
            }
            is String -> putString(key, data, ttl)
            else -> {
                println("DiskCache: Unsupported data type for key '$key': ${data::class.simpleName}")
            }
        }
    }

    /**
     * 存储字符串数据
     */
    suspend fun putString(key: String, data: String, ttl: Long) = withContext(Dispatchers.IO) {
        putDataInternal(key, data, ttl) { json.encodeToString(it) }
    }

    /**
     * 存储整数列表数据
     */
    suspend fun putIntList(key: String, data: List<Int>, ttl: Long) = withContext(Dispatchers.IO) {
        putDataInternal(key, data, ttl) { json.encodeToString(it) }
    }

    /**
     * 存储字符串列表数据
     */
    suspend fun putStringList(key: String, data: List<String>, ttl: Long) = withContext(Dispatchers.IO) {
        putDataInternal(key, data, ttl) { json.encodeToString(it) }
    }

    /**
     * 存储三元组数据
     */
    suspend fun putTriple(key: String, data: Triple<Int, Int, Int>, ttl: Long) = withContext(Dispatchers.IO) {
        val serializableData = SerializableTriple(data.first, data.second, data.third)
        putDataInternal(key, serializableData, ttl) { json.encodeToString(it) }
    }

    /**
     * 存储统计数据对
     */
    suspend fun putStatsPair(key: String, data: Pair<Long, Double>, ttl: Long) = withContext(Dispatchers.IO) {
        val serializableData = SerializablePair(data.first, data.second)
        putDataInternal(key, serializableData, ttl) { json.encodeToString(it) }
    }

    /**
     * 内部通用存储方法
     */
    private suspend fun <T> putDataInternal(key: String, data: T, ttl: Long, serializer: (T) -> String) {
        mutex.withLock {
            try {
                val cacheFile = getCacheFile(key)
                val jsonData = serializer(data)

                // 写入缓存文件
                cacheFile.writeText(jsonData)

                val currentTime = System.currentTimeMillis()
                val fileSize = cacheFile.length()

                // 更新元数据
                metadata.items[key] = CacheMetadata(
                    key = key,
                    timestamp = currentTime,
                    ttl = ttl,
                    lastAccessed = currentTime,
                    fileSize = fileSize
                )

                // 保存元数据
                saveMetadata()

                // 检查是否需要清理缓存
                checkAndCleanupIfNeeded()

                println("DiskCache: Cached data for key '$key' to disk (${fileSize} bytes)")

            } catch (e: Exception) {
                println("DiskCache: Failed to cache data for key '$key': ${e.message}")
                e.printStackTrace()
            }
        }
    }

    /**
     * 从磁盘缓存获取数据 - 字符串版本
     */
    suspend fun getString(key: String): String? = withContext(Dispatchers.IO) {
        getDataInternal(key) { jsonData ->
            json.decodeFromString<String>(jsonData)
        }
    }

    /**
     * 从磁盘缓存获取数据 - 整数列表版本
     */
    suspend fun getIntList(key: String): List<Int>? = withContext(Dispatchers.IO) {
        getDataInternal(key) { jsonData ->
            json.decodeFromString<List<Int>>(jsonData)
        }
    }

    /**
     * 从磁盘缓存获取数据 - 三元组版本
     */
    suspend fun getTriple(key: String): Triple<Int, Int, Int>? = withContext(Dispatchers.IO) {
        getDataInternal(key) { jsonData ->
            val serializableData = json.decodeFromString<SerializableTriple>(jsonData)
            Triple(serializableData.first, serializableData.second, serializableData.third)
        }
    }

    /**
     * 从磁盘缓存获取数据 - 统计数据版本
     */
    suspend fun getStatsPair(key: String): Pair<Long, Double>? = withContext(Dispatchers.IO) {
        getDataInternal(key) { jsonData ->
            val serializableData = json.decodeFromString<SerializablePair>(jsonData)
            Pair(serializableData.first, serializableData.second)
        }
    }

    /**
     * 内部通用获取方法
     */
    private suspend fun <T> getDataInternal(key: String, deserializer: (String) -> T): T? {
        mutex.withLock {
            try {
                val cacheMetadata = metadata.items[key]

                if (cacheMetadata == null) {
                    println("DiskCache: Cache miss for key '$key' (no metadata)")
                    return null
                }

                if (!cacheMetadata.isValid()) {
                    // 缓存已过期，删除文件和元数据
                    removeCacheFile(key)
                    metadata.items.remove(key)
                    saveMetadata()
                    println("DiskCache: Cache expired for key '$key', removed")
                    return null
                }

                val cacheFile = getCacheFile(key)
                if (!cacheFile.exists()) {
                    // 文件不存在，清理元数据
                    metadata.items.remove(key)
                    saveMetadata()
                    println("DiskCache: Cache file missing for key '$key', cleaned metadata")
                    return null
                }

                // 读取并反序列化数据
                val jsonData = cacheFile.readText()
                val data = deserializer(jsonData)

                // 更新访问时间
                metadata.items[key] = cacheMetadata.copy(lastAccessed = System.currentTimeMillis())
                saveMetadata()

                println("DiskCache: Cache hit for key '$key'")
                return data

            } catch (e: Exception) {
                println("DiskCache: Failed to read cache for key '$key': ${e.message}")
                // 如果读取失败，删除损坏的缓存
                removeCacheFile(key)
                metadata.items.remove(key)
                saveMetadata()
                return null
            }
        }
    }

    /**
     * 移除指定键的缓存
     */
    suspend fun remove(key: String) = withContext(Dispatchers.IO) {
        mutex.withLock {
            removeCacheFile(key)
            metadata.items.remove(key)
            saveMetadata()
            println("DiskCache: Removed cache for key '$key'")
        }
    }

    /**
     * 移除匹配前缀的所有缓存项
     */
    suspend fun removeByPrefix(prefix: String) = withContext(Dispatchers.IO) {
        mutex.withLock {
            val keysToRemove = metadata.items.keys.filter { it.startsWith(prefix) }
            keysToRemove.forEach { key ->
                removeCacheFile(key)
                metadata.items.remove(key)
            }
            saveMetadata()
            println("DiskCache: Removed ${keysToRemove.size} cache items with prefix '$prefix'")
        }
    }

    /**
     * 清除所有缓存
     */
    suspend fun clearAll() = withContext(Dispatchers.IO) {
        mutex.withLock {
            try {
                // 删除所有缓存文件
                cacheDir.listFiles()?.forEach { file ->
                    if (file.name.endsWith(CACHE_FILE_EXTENSION)) {
                        file.delete()
                    }
                }
                
                // 清空元数据
                val itemCount = metadata.items.size
                metadata.items.clear()
                saveMetadata()
                
                println("DiskCache: Cleared all disk cache ($itemCount items)")
                
            } catch (e: Exception) {
                println("DiskCache: Failed to clear all cache: ${e.message}")
            }
        }
    }

    /**
     * 清理过期的缓存项
     */
    suspend fun cleanupExpired() = withContext(Dispatchers.IO) {
        mutex.withLock {
            val expiredKeys = metadata.items.entries
                .filter { it.value.isExpired() }
                .map { it.key }
            
            expiredKeys.forEach { key ->
                removeCacheFile(key)
                metadata.items.remove(key)
            }
            
            if (expiredKeys.isNotEmpty()) {
                saveMetadata()
                println("DiskCache: Cleaned up ${expiredKeys.size} expired cache items")
            }
        }
    }

    /**
     * 获取缓存统计信息
     */
    suspend fun getCacheStats(): DiskCacheStats = withContext(Dispatchers.IO) {
        mutex.withLock {
            val totalItems = metadata.items.size
            val expiredItems = metadata.items.values.count { it.isExpired() }
            val validItems = totalItems - expiredItems
            val totalSize = metadata.items.values.sumOf { it.fileSize }
            
            DiskCacheStats(
                totalItems = totalItems,
                validItems = validItems,
                expiredItems = expiredItems,
                totalSizeBytes = totalSize,
                maxSizeBytes = MAX_DISK_CACHE_SIZE
            )
        }
    }

    // 私有辅助方法
    private fun getCacheFile(key: String): File {
        val safeKey = key.replace(Regex("[^a-zA-Z0-9_-]"), "_")
        return File(cacheDir, "$safeKey$CACHE_FILE_EXTENSION")
    }

    private fun removeCacheFile(key: String) {
        try {
            val cacheFile = getCacheFile(key)
            if (cacheFile.exists()) {
                cacheFile.delete()
            }
        } catch (e: Exception) {
            println("DiskCache: Failed to remove cache file for key '$key': ${e.message}")
        }
    }

    private fun loadMetadata() {
        try {
            if (metadataFile.exists()) {
                val jsonData = metadataFile.readText()
                metadata = json.decodeFromString<CacheMetadataCollection>(jsonData)
                println("DiskCache: Loaded metadata for ${metadata.items.size} cache items")
            }
        } catch (e: Exception) {
            println("DiskCache: Failed to load metadata, starting fresh: ${e.message}")
            metadata = CacheMetadataCollection()
        }
    }

    private fun saveMetadata() {
        try {
            val jsonData = json.encodeToString(metadata)
            metadataFile.writeText(jsonData)
        } catch (e: Exception) {
            println("DiskCache: Failed to save metadata: ${e.message}")
        }
    }

    private fun checkAndCleanupIfNeeded() {
        val totalSize = metadata.items.values.sumOf { it.fileSize }
        val threshold = MAX_DISK_CACHE_SIZE * CLEANUP_THRESHOLD
        
        if (totalSize > threshold) {
            println("DiskCache: Cache size ($totalSize bytes) exceeds threshold, starting cleanup")
            cleanupOldestItems()
        }
    }

    private fun cleanupOldestItems() {
        try {
            val itemsToRemove = metadata.items.entries
                .sortedBy { it.value.lastAccessed }
                .take(metadata.items.size / 4) // 移除25%的最旧项
                .map { it.key }
            
            itemsToRemove.forEach { key ->
                removeCacheFile(key)
                metadata.items.remove(key)
            }
            
            saveMetadata()
            println("DiskCache: Cleaned up ${itemsToRemove.size} oldest cache items")
            
        } catch (e: Exception) {
            println("DiskCache: Failed to cleanup oldest items: ${e.message}")
        }
    }

    /**
     * 磁盘缓存统计信息数据类
     */
    data class DiskCacheStats(
        val totalItems: Int,
        val validItems: Int,
        val expiredItems: Int,
        val totalSizeBytes: Long,
        val maxSizeBytes: Long
    ) {
        val usagePercentage: Double
            get() = if (maxSizeBytes > 0) (totalSizeBytes.toDouble() / maxSizeBytes) * 100 else 0.0
    }
}
