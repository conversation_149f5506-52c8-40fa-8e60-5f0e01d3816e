package com.lfb.android.footprint

import android.os.Bundle
import androidx.activity.ComponentActivity
import com.lfb.android.footprint.utils.ScreenManager

open class BaseActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        hideStatusBars() // 统一隐藏状态栏

        // 启用屏幕常亮，防止应用在前台时屏幕熄屏
        ScreenManager.enableKeepScreenOn(this)
    }

    override fun onResume() {
        super.onResume()
        // 确保在Activity恢复时屏幕常亮仍然有效
        ScreenManager.enableKeepScreenOn(this)
    }

    override fun onPause() {
        super.onPause()
        // 当Activity暂停时禁用屏幕常亮，节省电量
        ScreenManager.disableKeepScreenOn(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        // Activity销毁时确保释放屏幕常亮资源
        ScreenManager.disableKeepScreenOn(this)
    }
}