# DataCacheManager 使用指南

## 概述

`DataCacheManager` 是一个通用的数据缓存管理器，专门为 FootPrint 应用设计，用于缓存各种数据查询结果，显著提升应用性能，特别是在 DetailDataPanel 等数据密集型组件中。

## 主要特性

- **线程安全**: 使用 `ConcurrentHashMap` 和 `Mutex` 确保多线程环境下的安全性
- **TTL支持**: 每个缓存项都有生存时间，自动过期清理
- **LRU策略**: 当缓存达到最大容量时，自动清理最久未使用的项
- **智能键管理**: 预定义的缓存键和键构建器，避免键冲突
- **批量操作**: 支持按前缀批量清理缓存

## 预定义缓存键

```kotlin
// 基础数据缓存
DataCacheManager.CacheKeys.AVAILABLE_YEARS      // 可用年份列表
DataCacheManager.CacheKeys.LAST_DATA_DAY        // 最后一天数据
DataCacheManager.CacheKeys.LIFETIME_STATS       // 一生数据统计

// 动态键构建器
DataCacheManager.KeyBuilder.yearDays(2023)                    // 2023年所有日期
DataCacheManager.KeyBuilder.dayData(2023, 12, 25)            // 特定日期数据
DataCacheManager.KeyBuilder.dayCount(2023, 12, 25)           // 特定日期数据点数量
DataCacheManager.KeyBuilder.yearMonths(2023)                 // 2023年所有月份
DataCacheManager.KeyBuilder.monthDays(2023, 12)              // 2023年12月所有日期
```

## TTL 配置

```kotlin
DataCacheManager.CacheTTL.VERY_SHORT    // 30秒 - 频繁变化的数据
DataCacheManager.CacheTTL.SHORT         // 1分钟 - 一般查询数据
DataCacheManager.CacheTTL.MEDIUM        // 5分钟 - 相对稳定的数据
DataCacheManager.CacheTTL.LONG          // 30分钟 - 很少变化的数据
DataCacheManager.CacheTTL.VERY_LONG     // 1小时 - 基本不变的数据
```

## 在 RealmModelManager 中的集成

### 基本缓存模式

```kotlin
// 获取可用年份（带缓存）
suspend fun getAvailableYears(): List<Int> = withContext(Dispatchers.IO) {
    cacheManager.getOrCompute(
        key = DataCacheManager.CacheKeys.AVAILABLE_YEARS,
        ttl = DataCacheManager.CacheTTL.MEDIUM
    ) {
        getAvailableYearsFromDatabase() // 实际数据库查询
    }
}
```

### 动态键缓存模式

```kotlin
// 获取指定日期数据（带缓存）
suspend fun getDataPointsForDay(year: Int, month: Int, day: Int): List<StepDataRealmModel> {
    return cacheManager.getOrCompute(
        key = DataCacheManager.KeyBuilder.dayData(year, month, day),
        ttl = DataCacheManager.CacheTTL.MEDIUM
    ) {
        getDataPointsForDayFromDatabase(year, month, day)
    }
}
```

## 缓存清理策略

### 智能缓存清理

当数据发生变化时，使用智能清理方法：

```kotlin
// 数据变化时的智能缓存清理
suspend fun onDataChanged(year: Int? = null, month: Int? = null, day: Int? = null) {
    // 清除统计信息缓存
    clearStatsCache()
    
    // 清除年份列表缓存
    clearYearsCache()
    
    // 清除最后一天缓存
    clearLastDayCache()
    
    // 如果指定了具体日期，清除相关缓存
    if (year != null) {
        clearYearRelatedCache(year)
        
        if (month != null && day != null) {
            cacheManager.remove(DataCacheManager.KeyBuilder.dayData(year, month, day))
            cacheManager.remove(DataCacheManager.KeyBuilder.dayCount(year, month, day))
        }
    }
}
```

### 按前缀批量清理

```kotlin
// 清除指定年份的所有相关缓存
suspend fun clearYearRelatedCache(year: Int) {
    cacheManager.remove(DataCacheManager.KeyBuilder.yearDays(year))
    cacheManager.remove(DataCacheManager.KeyBuilder.yearMonths(year))
    cacheManager.removeByPrefix("${DataCacheManager.CacheKeys.DAY_DATA_PREFIX}${year}_")
    cacheManager.removeByPrefix("${DataCacheManager.CacheKeys.DAY_COUNT_PREFIX}${year}_")
}
```

## 在 UI 组件中的使用

### DetailDataPanel 优化示例

```kotlin
// 在 DetailDataPanel 中，数据加载现在会自动使用缓存
LaunchedEffect(Unit) {
    coroutineScope.launch {
        try {
            val realmManager = RealmModelManager.getInstance()
            
            // 这些调用现在都会使用缓存，显著提升性能
            availableYears = realmManager.getAvailableYears()           // 缓存5分钟
            val lastDay = realmManager.getLastDataDay()                // 缓存1分钟
            
            lastDay?.let { (year, month, day) ->
                selectedYear = year
                selectedMonth = month
                selectedDay = day
                
                availableDays = realmManager.getAllDaysInYear(year)     // 缓存5分钟
                dataPoints = realmManager.getDataPointsForDay(year, month, day) // 缓存5分钟
                
                onDayDataChanged(dataPoints)
            }
            isLoading = false
        } catch (e: Exception) {
            isLoading = false
        }
    }
}
```

## 性能监控

### 获取缓存统计信息

```kotlin
val cacheManager = DataCacheManager.getInstance()
val stats = cacheManager.getCacheStats()

println("缓存统计:")
println("总项目数: ${stats.totalItems}")
println("有效项目数: ${stats.validItems}")
println("过期项目数: ${stats.expiredItems}")
println("最大容量: ${stats.maxSize}")
```

### 手动清理过期项

```kotlin
// 定期清理过期缓存项
cacheManager.cleanupExpired()
```

## 最佳实践

1. **选择合适的TTL**: 根据数据变化频率选择合适的缓存时间
2. **及时清理**: 数据变更时及时清理相关缓存
3. **监控性能**: 定期检查缓存统计信息，优化缓存策略
4. **避免过度缓存**: 不要缓存过于频繁变化的数据
5. **使用预定义键**: 使用 `CacheKeys` 和 `KeyBuilder` 避免键冲突

## 注意事项

- 缓存管理器是单例模式，全局共享
- 所有操作都是协程安全的
- 缓存项会自动过期，无需手动管理生命周期
- 当缓存达到最大容量(1000项)时，会自动清理25%的最旧项
