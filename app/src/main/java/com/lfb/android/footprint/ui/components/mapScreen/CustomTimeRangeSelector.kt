package com.lfb.android.footprint.ui.components.mapScreen

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.datetime.*
import java.text.SimpleDateFormat
import java.util.*
import com.lfb.android.footprint.ui.theme.rememberMapThemeConfig
import com.lfb.android.footprint.prefs.AppPrefs
import com.lfb.android.footprint.ui.theme.mainRedColor

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CustomTimeRangeSelector(
    startTime: Long,
    endTime: Long,
    onTimeRangeChanged: (Long, Long) -> Unit,
    modifier: Modifier = Modifier
) {
    val themeConfig = rememberMapThemeConfig()
    var showStartDatePicker by remember { mutableStateOf(false) }
    var showEndDatePicker by remember { mutableStateOf(false) }

    val dateFormatter = remember { SimpleDateFormat("yyyy.MM.dd", Locale.getDefault()) }

    // 转换时间戳为日期显示
    val startDateText = remember(startTime) {
        dateFormatter.format(Date(startTime * 1000))
    }

    val endDateText = remember(endTime) {
        dateFormatter.format(Date(endTime * 1000))
    }

    Column(
        modifier = modifier
            .width(200.dp)
            .shadow(
                elevation = 2.dp,
                shape = RoundedCornerShape(12.dp)
            )
            .background(
                color = themeConfig.backgroundColor,
                shape = RoundedCornerShape(12.dp)
            )
    ) {

        Text(
            modifier = Modifier.padding(start = 18.dp, top = 12.dp, bottom = 6.dp),
            text = "自定义时间范围",
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            color = themeConfig.textColor
        )

        // 开始时间区域 - 整个上半部分都可以点击
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { showStartDatePicker = true }
                .padding(horizontal = 18.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "开始:",
                fontSize = 14.sp,
                color = themeConfig.textColor
            )
            Text(
                text = startDateText,
                fontSize = 14.sp,
                color = themeConfig.textColor
            )
        }

        // 分隔线 - 只覆盖时间文字部分
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧空白区域（对应"开始:"/"结束:"标签的位置）
            Spacer(modifier = Modifier.width(80.dp))

            // 分隔线（只覆盖时间文字区域）
            Box(
                modifier = Modifier
                    .weight(1f)
                    .height(1.dp)
                    .background(themeConfig.textColor.copy(alpha = 0.5f))
            )
        }

        // 结束时间区域 - 整个下半部分都可以点击
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { showEndDatePicker = true }
                .padding(horizontal = 18.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "结束:",
                fontSize = 14.sp,
                color = mainRedColor
            )
            Text(
                text = endDateText,
                fontSize = 14.sp,
                color = mainRedColor
            )
        }
    }

    // 日期选择器对话框
    if (showStartDatePicker) {
        DatePickerDialog(
            initialDate = startTime,
            onDateSelected = { selectedTime ->
                onTimeRangeChanged(selectedTime, endTime)
                showStartDatePicker = false
            },
            onDismiss = { showStartDatePicker = false }
        )
    }

    if (showEndDatePicker) {
        DatePickerDialog(
            initialDate = endTime,
            onDateSelected = { selectedTime ->
                onTimeRangeChanged(startTime, selectedTime)
                showEndDatePicker = false
            },
            onDismiss = { showEndDatePicker = false }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DatePickerDialog(
    initialDate: Long,
    onDateSelected: (Long) -> Unit,
    onDismiss: () -> Unit
) {
    val datePickerState = rememberDatePickerState(
        initialSelectedDateMillis = initialDate * 1000
    )
    
    DatePickerDialog(
        onDismissRequest = onDismiss,
        confirmButton = {
            TextButton(
                onClick = {
                    datePickerState.selectedDateMillis?.let { millis ->
                        // 转换为当天开始的时间戳（秒）
                        val instant = Instant.fromEpochMilliseconds(millis)
                        val localDate = instant.toLocalDateTime(TimeZone.currentSystemDefault()).date
                        val epochSeconds = localDate.atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds
                        onDateSelected(epochSeconds)
                    }
                }
            ) {
                Text("确认")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    ) {
        DatePicker(state = datePickerState)
    }
}
