package com.lfb.android.footprint.Manager

import android.content.Context
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 磁盘缓存使用示例
 * 展示如何在实际应用中使用磁盘缓存功能
 */
object DiskCacheUsageExample {

    /**
     * 在应用启动时测试磁盘缓存功能
     * 可以在 MyApplication.onCreate() 中调用此方法
     */
    fun testDiskCacheOnAppStart(context: Context) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                println("=== 开始测试磁盘缓存功能 ===")
                
                val realmManager = RealmModelManager.getInstance(context)
                
                // 测试1: 获取可用年份（这会自动使用磁盘缓存）
                val startTime = System.currentTimeMillis()
                val years = realmManager.getAvailableYears()
                val endTime = System.currentTimeMillis()
                
                println("可用年份查询完成:")
                println("  结果: $years")
                println("  耗时: ${endTime - startTime}ms")
                
                // 测试2: 获取统计信息
                val statsStartTime = System.currentTimeMillis()
                val (totalCount, totalDistance, totalDays) = realmManager.getLifetimeDataStatsCompat()
                val statsEndTime = System.currentTimeMillis()

                println("统计信息查询完成:")
                println("  结果: 轨迹点数=$totalCount, 总距离=${totalDistance}km, 累计天数=${totalDays}天")
                println("  耗时: ${statsEndTime - statsStartTime}ms")
                
                // 显示缓存使用情况
                showCacheUsage(context)
                
                // 测试3: 两阶段缓存策略
                testTwoStageCacheStrategy(realmManager)

                println("=== 磁盘缓存功能测试完成 ===")

            } catch (e: Exception) {
                println("磁盘缓存测试失败: ${e.message}")
                e.printStackTrace()
            }
        }
    }

    /**
     * 测试两阶段缓存策略
     */
    private suspend fun testTwoStageCacheStrategy(realmManager: RealmModelManager) {
        println("\n=== 测试两阶段缓存策略 ===")

        // 第一次调用：测试立即返回缓存数据和异步更新
        var updateCallbackInvoked = false
        val startTime = System.currentTimeMillis()

        println("调用 getLifetimeDataStats...")
        val initialResult = realmManager.getLifetimeDataStats { updatedStats ->
            updateCallbackInvoked = true
            println("🔄 缓存更新回调被调用:")
            println("  更新后数据: 轨迹点数=${updatedStats.first}, 总距离=${updatedStats.second}km, 累计天数=${updatedStats.third}天")
        }

        val endTime = System.currentTimeMillis()

        println("✅ 第一阶段（立即返回）:")
        println("  结果: 轨迹点数=${initialResult.first}, 总距离=${initialResult.second}km, 累计天数=${initialResult.third}天")
        println("  耗时: ${endTime - startTime}ms (应该很快)")
        println("  异步更新已启动，等待回调...")

        // 等待一段时间让异步更新完成
        kotlinx.coroutines.delay(3000)

        if (updateCallbackInvoked) {
            println("✅ 异步更新成功完成")
        } else {
            println("⚠️ 异步更新未完成或数据未发生变化")
        }

        // 第二次调用：应该返回更新后的缓存数据
        val secondStartTime = System.currentTimeMillis()
        val secondResult = realmManager.getLifetimeDataStatsCompat()
        val secondEndTime = System.currentTimeMillis()

        println("✅ 第二次调用（验证缓存更新）:")
        println("  结果: 轨迹点数=${secondResult.first}, 总距离=${secondResult.second}km, 累计天数=${secondResult.third}天")
        println("  耗时: ${secondEndTime - secondStartTime}ms (应该很快)")

        println("=== 两阶段缓存策略测试完成 ===\n")
    }

    /**
     * 显示缓存使用情况
     */
    private suspend fun showCacheUsage(context: Context) {
        try {
            val cacheManager = DataCacheManager.getInstance(context)
            val stats = cacheManager.getCacheStats()
            
            println("=== 缓存使用情况 ===")
            println("内存缓存:")
            println("  有效项目: ${stats.validItems}")
            println("  总项目: ${stats.totalItems}")
            println("  过期项目: ${stats.expiredItems}")
            
            stats.diskStats?.let { diskStats ->
                println("磁盘缓存:")
                println("  有效项目: ${diskStats.validItems}")
                println("  总项目: ${diskStats.totalItems}")
                println("  总大小: ${formatBytes(diskStats.totalSizeBytes)}")
                println("  使用率: ${diskStats.usagePercentage.toInt()}%")
            } ?: println("磁盘缓存: 未启用")
            
        } catch (e: Exception) {
            println("获取缓存统计失败: ${e.message}")
        }
    }

    /**
     * 格式化字节数
     */
    private fun formatBytes(bytes: Long): String {
        return when {
            bytes < 1024 -> "${bytes}B"
            bytes < 1024 * 1024 -> "${bytes / 1024}KB"
            bytes < 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024)}MB"
            else -> "${bytes / (1024 * 1024 * 1024)}GB"
        }
    }

    /**
     * 清理所有缓存（用于测试或重置）
     */
    fun clearAllCaches(context: Context) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val cacheManager = DataCacheManager.getInstance(context)
                cacheManager.clearAll()
                println("所有缓存已清理")
            } catch (e: Exception) {
                println("清理缓存失败: ${e.message}")
            }
        }
    }

    /**
     * 手动清理过期缓存
     */
    fun cleanupExpiredCaches(context: Context) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val cacheManager = DataCacheManager.getInstance(context)
                cacheManager.cleanupExpired()
                println("过期缓存已清理")
            } catch (e: Exception) {
                println("清理过期缓存失败: ${e.message}")
            }
        }
    }
}

/**
 * 在 MyApplication 中的使用示例:
 * 
 * class MyApplication : Application() {
 *     override fun onCreate() {
 *         super.onCreate()
 *         AppPrefs.init(this)
 *         
 *         // 初始化 RealmModelManager（支持磁盘缓存）
 *         RealmModelManager.getInstance(this)
 *         
 *         // 可选：测试磁盘缓存功能
 *         if (BuildConfig.DEBUG) {
 *             DiskCacheUsageExample.testDiskCacheOnAppStart(this)
 *         }
 *         
 *         ProcessLifecycleOwner.get().lifecycle.addObserver(AppLifecycleObserver(this))
 *     }
 * }
 */
