package com.lfb.android.footprint.utils

import android.Manifest
import android.app.Activity
import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

/**
 * 权限管理器
 * 处理定位权限的申请和管理
 */
class PermissionManager(private val activity: Activity) {

    companion object {
        private const val LOCATION_PERMISSION_REQUEST_CODE = 1001
        private const val BACKGROUND_LOCATION_PERMISSION_REQUEST_CODE = 1002
    }

    // 权限申请回调
    private var onPermissionResult: ((Boolean) -> Unit)? = null

    /**
     * 检查是否有所有必需的定位权限
     */
    fun hasAllLocationPermissions(): Boolean {
        val fineLocationGranted = ContextCompat.checkSelfPermission(
            activity, Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED

        val coarseLocationGranted = ContextCompat.checkSelfPermission(
            activity, Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED

        val backgroundLocationGranted = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            ContextCompat.checkSelfPermission(
                activity, Manifest.permission.ACCESS_BACKGROUND_LOCATION
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true // Android 10以下不需要后台定位权限
        }

        return (fineLocationGranted || coarseLocationGranted) && backgroundLocationGranted
    }

    /**
     * 申请定位权限
     * @param callback 权限申请结果回调
     */
    fun requestLocationPermissions(callback: (Boolean) -> Unit) {
        onPermissionResult = callback

        if (hasAllLocationPermissions()) {
            callback(true)
            return
        }

        // 先申请基础定位权限
        requestBasicLocationPermissions()
    }

    /**
     * 申请基础定位权限（精确位置和粗略位置）
     */
    private fun requestBasicLocationPermissions() {
        val permissions = mutableListOf<String>()

        if (ContextCompat.checkSelfPermission(activity, Manifest.permission.ACCESS_FINE_LOCATION)
            != PackageManager.PERMISSION_GRANTED) {
            permissions.add(Manifest.permission.ACCESS_FINE_LOCATION)
        }

        if (ContextCompat.checkSelfPermission(activity, Manifest.permission.ACCESS_COARSE_LOCATION)
            != PackageManager.PERMISSION_GRANTED) {
            permissions.add(Manifest.permission.ACCESS_COARSE_LOCATION)
        }

        // 添加前台服务定位权限（Android 14+）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            if (ContextCompat.checkSelfPermission(activity, Manifest.permission.FOREGROUND_SERVICE_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {
                permissions.add(Manifest.permission.FOREGROUND_SERVICE_LOCATION)
            }
        }

        if (permissions.isNotEmpty()) {
            ActivityCompat.requestPermissions(activity, permissions.toTypedArray(), LOCATION_PERMISSION_REQUEST_CODE)
        } else {
            // 基础权限已有，直接申请后台权限
            requestBackgroundLocationPermission()
        }
    }

    /**
     * 申请后台定位权限
     */
    private fun requestBackgroundLocationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            if (ContextCompat.checkSelfPermission(activity, Manifest.permission.ACCESS_BACKGROUND_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {

                // 显示后台定位权限说明对话框
                showBackgroundLocationRationale()
            } else {
                // 后台定位权限已有
                onPermissionResult?.invoke(true)
            }
        } else {
            // Android 10以下不需要后台定位权限
            onPermissionResult?.invoke(true)
        }
    }

    /**
     * 显示后台定位权限说明对话框
     */
    private fun showBackgroundLocationRationale() {
        AlertDialog.Builder(activity)
            .setTitle("需要后台定位权限")
            .setMessage("为了持续记录您的运动轨迹，我们需要在应用处于后台时访问位置信息。请在接下来的系统对话框中选择\"始终允许\"。")
            .setPositiveButton("确定") { _, _ ->
                ActivityCompat.requestPermissions(
                    activity,
                    arrayOf(Manifest.permission.ACCESS_BACKGROUND_LOCATION),
                    BACKGROUND_LOCATION_PERMISSION_REQUEST_CODE
                )
            }
            .setNegativeButton("取消") { _, _ ->
                onPermissionResult?.invoke(false)
            }
            .setCancelable(false)
            .show()
    }

    /**
     * 显示基础定位权限被拒绝的对话框
     */
    private fun showPermissionDeniedDialog() {
        AlertDialog.Builder(activity)
            .setTitle("权限被拒绝")
            .setMessage("定位权限是应用正常运行的必要条件，请在设置中手动开启。")
            .setPositiveButton("去设置") { _, _ ->
                openAppSettings()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 显示后台定位权限被拒绝的对话框
     */
    private fun showBackgroundLocationDeniedDialog() {
        AlertDialog.Builder(activity)
            .setTitle("后台定位权限被拒绝")
            .setMessage("没有后台定位权限，应用将无法在后台记录轨迹。您可以稍后在设置中手动开启。")
            .setPositiveButton("去设置") { _, _ ->
                openAppSettings()
            }
            .setNegativeButton("稍后再说", null)
            .show()
    }

    /**
     * 处理权限申请结果
     * 需要在Activity的onRequestPermissionsResult中调用
     */
    fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        when (requestCode) {
            LOCATION_PERMISSION_REQUEST_CODE -> {
                val fineLocationGranted = grantResults.isNotEmpty() &&
                    permissions.contains(Manifest.permission.ACCESS_FINE_LOCATION) &&
                    grantResults[permissions.indexOf(Manifest.permission.ACCESS_FINE_LOCATION)] == PackageManager.PERMISSION_GRANTED

                val coarseLocationGranted = grantResults.isNotEmpty() &&
                    permissions.contains(Manifest.permission.ACCESS_COARSE_LOCATION) &&
                    grantResults[permissions.indexOf(Manifest.permission.ACCESS_COARSE_LOCATION)] == PackageManager.PERMISSION_GRANTED

                if (fineLocationGranted || coarseLocationGranted) {
                    // 基础定位权限已获得，继续申请后台定位权限
                    requestBackgroundLocationPermission()
                } else {
                    // 基础定位权限被拒绝
                    onPermissionResult?.invoke(false)
                    showPermissionDeniedDialog()
                }
            }
            BACKGROUND_LOCATION_PERMISSION_REQUEST_CODE -> {
                val isGranted = grantResults.isNotEmpty() &&
                    grantResults[0] == PackageManager.PERMISSION_GRANTED

                onPermissionResult?.invoke(isGranted)
                if (!isGranted) {
                    showBackgroundLocationDeniedDialog()
                }
            }
        }
    }

    /**
     * 打开应用设置页面
     */
    private fun openAppSettings() {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.fromParts("package", activity.packageName, null)
        }
        activity.startActivity(intent)
    }
}
