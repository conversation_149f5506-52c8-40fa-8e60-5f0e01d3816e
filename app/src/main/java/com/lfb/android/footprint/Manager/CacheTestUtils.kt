package com.lfb.android.footprint.Manager

import android.content.Context
import kotlinx.coroutines.delay

/**
 * 缓存功能测试工具类
 * 用于验证磁盘缓存功能是否正常工作
 */
object CacheTestUtils {

    /**
     * 测试磁盘缓存功能
     * 这个方法可以在应用启动时调用，验证缓存是否正常工作
     */
    suspend fun testDiskCacheFunction(context: Context) {
        println("=== 开始测试磁盘缓存功能 ===")
        
        val cacheManager = DataCacheManager.getInstance(context)
        val testKey = "test_disk_cache_key"
        val testData = listOf("test1", "test2", "test3")
        val ttl = 60000L // 1分钟
        
        try {
            // 1. 清除可能存在的测试缓存
            cacheManager.remove(testKey)
            println("1. 清除旧的测试缓存")
            
            // 2. 存储测试数据
            cacheManager.put(testKey, testData, ttl)
            println("2. 存储测试数据到缓存: $testData")
            
            // 3. 立即读取（应该从内存缓存获取）
            val memoryResult = cacheManager.get<List<String>>(testKey)
            println("3. 从内存缓存读取: $memoryResult")

            // 4. 清除内存缓存，但保留磁盘缓存
            cacheManager.clearMemoryCache()
            println("4. 清除内存缓存")

            // 5. 再次读取（应该从磁盘缓存恢复）
            val diskResult = cacheManager.get<List<String>>(testKey)
            println("5. 从磁盘缓存恢复: $diskResult")
            
            // 6. 验证结果
            if (diskResult == testData) {
                println("✅ 磁盘缓存功能测试成功！")
            } else {
                println("❌ 磁盘缓存功能测试失败！期望: $testData, 实际: $diskResult")
            }
            
            // 7. 获取缓存统计信息
            val stats = cacheManager.getCacheStats()
            println("6. 缓存统计信息:")
            println("   内存缓存: ${stats.validItems}/${stats.totalItems}")
            stats.diskStats?.let { diskStats ->
                println("   磁盘缓存: ${diskStats.validItems}/${diskStats.totalItems}")
                println("   磁盘使用: ${diskStats.totalSizeBytes} bytes")
            }
            
            // 8. 清理测试数据
            cacheManager.remove(testKey)
            println("7. 清理测试数据")
            
        } catch (e: Exception) {
            println("❌ 磁盘缓存测试过程中发生错误: ${e.message}")
            e.printStackTrace()
        }
        
        println("=== 磁盘缓存功能测试完成 ===")
    }

    /**
     * 测试实际数据的缓存性能
     */
    suspend fun testRealDataCachePerformance(context: Context) {
        println("=== 开始测试实际数据缓存性能 ===")
        
        val realmManager = RealmModelManager.getInstance(context)
        
        try {
            // 测试可用年份查询性能
            println("1. 测试可用年份查询...")
            
            // 第一次查询（可能从数据库）
            val startTime1 = System.currentTimeMillis()
            val years1 = realmManager.getAvailableYears()
            val time1 = System.currentTimeMillis() - startTime1
            println("   第一次查询耗时: ${time1}ms, 结果: $years1")
            
            // 第二次查询（应该从内存缓存）
            val startTime2 = System.currentTimeMillis()
            val years2 = realmManager.getAvailableYears()
            val time2 = System.currentTimeMillis() - startTime2
            println("   第二次查询耗时: ${time2}ms, 结果: $years2")
            
            // 清除内存缓存
            val cacheManager = DataCacheManager.getInstance(context)
            cacheManager.clearMemoryCache()
            println("2. 清除内存缓存")
            
            // 第三次查询（应该从磁盘缓存恢复）
            val startTime3 = System.currentTimeMillis()
            val years3 = realmManager.getAvailableYears()
            val time3 = System.currentTimeMillis() - startTime3
            println("   第三次查询耗时: ${time3}ms, 结果: $years3")
            
            // 性能分析
            println("3. 性能分析:")
            println("   内存缓存加速比: ${if (time2 > 0) time1.toFloat() / time2 else "无限"}x")
            println("   磁盘缓存加速比: ${if (time3 > 0) time1.toFloat() / time3 else "无限"}x")
            
            if (time2 < time1 && time3 < time1) {
                println("✅ 缓存性能测试成功！两层缓存都有效提升了查询速度")
            } else {
                println("⚠️ 缓存性能可能需要优化")
            }
            
        } catch (e: Exception) {
            println("❌ 实际数据缓存性能测试过程中发生错误: ${e.message}")
            e.printStackTrace()
        }
        
        println("=== 实际数据缓存性能测试完成 ===")
    }

    /**
     * 显示缓存使用情况
     */
    suspend fun showCacheUsage(context: Context) {
        println("=== 缓存使用情况 ===")
        
        try {
            val cacheManager = DataCacheManager.getInstance(context)
            val stats = cacheManager.getCacheStats()
            
            println("内存缓存:")
            println("  总项目数: ${stats.totalItems}")
            println("  有效项目数: ${stats.validItems}")
            println("  过期项目数: ${stats.expiredItems}")
            println("  最大容量: ${stats.maxSize}")
            println("  使用率: ${if (stats.maxSize > 0) (stats.totalItems.toFloat() / stats.maxSize * 100).toInt() else 0}%")
            
            stats.diskStats?.let { diskStats ->
                println("磁盘缓存:")
                println("  总项目数: ${diskStats.totalItems}")
                println("  有效项目数: ${diskStats.validItems}")
                println("  过期项目数: ${diskStats.expiredItems}")
                println("  总大小: ${formatBytes(diskStats.totalSizeBytes)}")
                println("  最大大小: ${formatBytes(diskStats.maxSizeBytes)}")
                println("  使用率: ${diskStats.usagePercentage.toInt()}%")
            } ?: println("磁盘缓存: 未启用或不可用")
            
        } catch (e: Exception) {
            println("❌ 获取缓存使用情况时发生错误: ${e.message}")
        }
        
        println("=== 缓存使用情况显示完成 ===")
    }

    /**
     * 格式化字节数显示
     */
    private fun formatBytes(bytes: Long): String {
        return when {
            bytes < 1024 -> "${bytes}B"
            bytes < 1024 * 1024 -> "${bytes / 1024}KB"
            bytes < 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024)}MB"
            else -> "${bytes / (1024 * 1024 * 1024)}GB"
        }
    }
}


