package com.lfb.android.footprint.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.material.icons.Icons
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.sp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.material.icons.filled.Map
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import com.lfb.android.footprint.R
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.text.font.FontWeight

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ConfigPanel(modifier: Modifier = Modifier, onClose: () -> Unit) {

    ModalBottomSheet(
        onDismissRequest = onClose,
        modifier = modifier
            .fillMaxWidth()
            .height(400.dp) // Increased height to match design
                ,
        shape = RoundedCornerShape(topStart = 22.dp, topEnd = 22.dp),
        containerColor = Color(0xFF000000), // Pure black background to match design
        dragHandle = null // 添加这一行来移除拖拽手柄
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
//                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp, vertical = 8.dp)
        ) {
            // Map Display Section
            var showPhotoData by remember { mutableStateOf(false) }
            var showAnnotationData by remember { mutableStateOf(false) }
            var showMapNames by remember { mutableStateOf(false) }

            Spacer(modifier = Modifier.height(24.dp))

            ConfigItem(
                label = "| 地图显示",
                content = {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(6.dp)
                    ) {
//                        ConfigToggleButton(
//                            text = "显示照片数据",
//                            isChecked = showPhotoData,
//                            onCheckedChange = { showPhotoData = it },
//                            modifier = Modifier.weight(1f)
//                        )
//                        ConfigToggleButton(
//                            text = "显示标注数据",
//                            isChecked = showAnnotationData,
//                            onCheckedChange = { showAnnotationData = it },
//                            modifier = Modifier.weight(1f)
//                        )
                        ConfigToggleButton(
                            text = "地图显示地名",
                            isChecked = showMapNames,
                            onCheckedChange = { showMapNames = it },
                            modifier = Modifier.width(94.dp)
                        )
                    }
                }
            )
            Spacer(modifier = Modifier.height(12.dp))

            // Map Style Section
            var selectedMapStyle by remember { mutableStateOf("通用地图") }

            ConfigItem(
                label = "| 地图样式",
                content = {
                    LazyRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        item {
                            MapStyleButton(
                                text = "通用地图",
                                isSelected = selectedMapStyle == "通用地图",
                                onClick = { selectedMapStyle = "通用地图" },
                                backgroundImage = painterResource(id = R.drawable.config_normalmap)
                            )
                        }
                        item {
                            MapStyleButton(
                                text = "清新蓝",
                                isSelected = selectedMapStyle == "清新蓝",
                                onClick = { selectedMapStyle = "清新蓝" },
                                backgroundImage = painterResource(id = R.drawable.config_map_gray)
                            )
                        }
                        item {
                            MapStyleButton(
                                text = "象牙白",
                                isSelected = selectedMapStyle == "象牙白",
                                onClick = { selectedMapStyle = "象牙白" },
                                backgroundImage = painterResource(id = R.drawable.config_map_white)
                            )
                        }
                        item {
                            MapStyleButton(
                                text = "卫星图",
                                isSelected = selectedMapStyle == "卫星图",
                                onClick = { selectedMapStyle = "卫星图" },
                                backgroundImage = painterResource(id = R.drawable.config_map_satellite)
                            )
                        }
                        item {
                            MapStyleButton(
                                text = "户外地图",
                                isSelected = selectedMapStyle == "户外地图",
                                onClick = { selectedMapStyle = "户外地图" },
                                backgroundImage = painterResource(id = R.drawable.config_map_outdoors)
                            )
                        }
                    }
                }
            )
            Spacer(modifier = Modifier.height(12.dp))

            // Track Color Section - matching design colors exactly
            var selectedTrackColor by remember { mutableStateOf(Color(0xFFFF0000)) }
            val trackColors = listOf(
                Color(0xFFFF0000), Color(0xFFEE5147), Color(0xFFF78356), Color(0xFFF8954A),
                Color(0xFFCBCB2D), Color(0xFFB1D441), Color(0xFF6AD070), Color(0xFF45C79D),
                Color(0xFF53AAEB), Color(0xFF007AFB), Color(0xFF638AF1), Color(0xFF877BFC),
                Color(0xFF7C2DED), Color(0xFFB684EE), Color(0xFFE286EE), Color(0xFFF789CE),
                Color(0xFFF77196), Color(0xFF5697A8), Color(0xFFDBD9DB), Color(0xFFF3F1F3),
                Color(0xFFFFFFFF), Color(0xFF222222)
            )

            ConfigItem(
                label = "| 轨迹颜色",
                content = {
                    LazyRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        trackColors.forEach { color ->
                            item {
                                ColorSelectionItem(
                                    color = color,
                                    isSelected = selectedTrackColor == color,
                                    onClick = { selectedTrackColor = color },
                                    modifier = Modifier.size(32.dp)
                                )
                            }
                        }
                    }
                }
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Sliders - matching design with red color scheme
            var trackLineOpacity by remember { mutableStateOf(0.8f) }
            var trackPointOpacity by remember { mutableStateOf(0.8f) }
            var trackLineWidth by remember { mutableStateOf(4f) }
            var trackConnectionDistance by remember { mutableStateOf(0.5f) }
            var hideHighAltitudeTrack by remember { mutableStateOf(0.8f) }

            ConfigItem(
                label = "| 轨迹线透明度",
                content = {
                    Slider(
                        value = trackLineOpacity,
                        onValueChange = { trackLineOpacity = it },
                        valueRange = 0f..1f,
                        colors = SliderDefaults.colors(
                            activeTrackColor = Color(0xFFE53935),
                            thumbColor = Color(0xFFE53935),
                            inactiveTrackColor = Color(0xFF404040)
                        ),
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            )

            ConfigItem(
                label = "| 轨迹点透明度",
                content = {
                    Slider(
                        value = trackPointOpacity,
                        onValueChange = { trackPointOpacity = it },
                        valueRange = 0f..1f,
                        colors = SliderDefaults.colors(
                            activeTrackColor = Color(0xFFE53935),
                            thumbColor = Color(0xFFE53935),
                            inactiveTrackColor = Color(0xFF404040)
                        ),
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            )

            ConfigItem(
                label = "| 轨迹线宽度",
                content = {
                    Slider(
                        value = trackLineWidth,
                        onValueChange = { trackLineWidth = it },
                        valueRange = 1f..10f,
                        steps = 9,
                        colors = SliderDefaults.colors(
                            activeTrackColor = Color(0xFFE53935),
                            thumbColor = Color(0xFFE53935),
                            inactiveTrackColor = Color(0xFF404040)
                        ),
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            )


            ConfigItem(
                label = "| 轨迹连接距离",
                content = {
                    Slider(
                        value = trackConnectionDistance,
                        onValueChange = { trackConnectionDistance = it },
                        valueRange = 0f..1f,
                        colors = SliderDefaults.colors(
                            activeTrackColor = Color(0xFFE53935),
                            thumbColor = Color(0xFFE53935),
                            inactiveTrackColor = Color(0xFF404040)
                        ),
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            )
            Spacer(modifier = Modifier.height(20.dp))
        }
    }
}

@Composable
fun ConfigItem(label: String, date: String? = null, content: @Composable () -> Unit) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            color = Color.White,
            style = MaterialTheme.typography.bodySmall,
            modifier = Modifier.width(88.dp) // 固定宽度确保对齐
        )
        if (date != null) {
            Text(
                text = date,
                color = Color.White.copy(alpha = 0.6f),
                style = MaterialTheme.typography.bodySmall,
//                modifier = Modifier.padding(end = 8.dp)
            )
        }
        Box(modifier = Modifier.weight(1f)) {
            content()
        }
    }
}

@Composable
fun ConfigToggleButton(
    text: String,
    isChecked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .height(28.dp)
            .border(
                width = 1.dp,
                color = if (isChecked) Color(0xFFE53935) else Color(0xFF666666),
                shape = RoundedCornerShape(14.dp)
            )
            .background(
                color = Color.Transparent,
                shape = RoundedCornerShape(14.dp)
            )
            .clickable { onCheckedChange(!isChecked) }
            .padding(horizontal = 8.dp, vertical = 2.dp)
        ,
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            color = if (isChecked) Color(0xFFE53935) else Color(0xFF999999),
            style = MaterialTheme.typography.labelSmall,
            fontSize = 10.sp
        )
    }
}

@Composable
fun MapStyleButton(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    backgroundImage: Painter? = null
) {
    // Map style button with background image and text overlay
    Box(
        modifier = modifier
            .size(width = 94.dp, height = 40.dp)
            .border(
                width = if (isSelected) 0.7.dp else 0.dp,
                color = if (isSelected) Color(0xFFE53935) else Color(0xFF666666),
                shape = RoundedCornerShape(12.dp)
            )
            .background(
                color = Color(0xFF1A1A1A),
                shape = RoundedCornerShape(12.dp)
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        // Background image
        if (backgroundImage != null) {
            Image(
                painter = backgroundImage,
                contentDescription = null,
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(12.dp)),
                contentScale = ContentScale.Crop
            )
        }

        // Semi-transparent overlay for better text readability
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    color = Color.Black.copy(alpha = 0.2f),
                    shape = RoundedCornerShape(12.dp)
                )
        )

        // Text overlay on the background
        Text(
            text = text,
            color = Color.White,
            style = MaterialTheme.typography.labelSmall,
            fontSize = 11.sp,
            modifier = Modifier
                .padding(start = 12.dp) // 添加左侧间距
                .align(alignment = Alignment.CenterStart) // 使文本靠左对齐
        )
    }
}

@Composable
fun ConfigSlider(label: String, value: Float, onValueChange: (Float) -> Unit, valueRange: ClosedFloatingPointRange<Float>, steps: Int) {
    Column(modifier = Modifier.fillMaxWidth()) {
        Text(
            text = label,
            color = Color.White,
            style = MaterialTheme.typography.bodySmall,
            fontSize = 14.sp
        )
        Spacer(modifier = Modifier.height(8.dp))
        Slider(
            value = value,
            onValueChange = onValueChange,
            valueRange = valueRange,
            steps = steps,
            colors = SliderDefaults.colors(
                activeTrackColor = Color(0xFFE53935), // Red to match design
                thumbColor = Color(0xFFE53935), // Red thumb
                inactiveTrackColor = Color(0xFF404040) // Lighter grey for inactive track
            ),
            modifier = Modifier.fillMaxWidth()
        )
    }
}

@Composable
fun ConfigSwitchToggle(label: String, isChecked: Boolean, onCheckedChange: (Boolean) -> Unit) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(text = label, color = Color.White, style = MaterialTheme.typography.bodySmall)
        Switch(checked = isChecked, onCheckedChange = onCheckedChange,
            colors = SwitchDefaults.colors(
                checkedThumbColor = Color(0xFFB00020),
                checkedTrackColor = Color(0xFF2A2A2A),
                uncheckedThumbColor = Color.White,
                uncheckedTrackColor = Color(0xFF2A2A2A)
            )
        )
    }
}

@Composable
fun ColorSelectionItem(color: Color, isSelected: Boolean, onClick: () -> Unit, modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .aspectRatio(1f) // Ensure perfect circle
            .background(color, CircleShape)
            .clickable(onClick = onClick)
            .border(
                width = if (isSelected) 2.dp else 0.dp,
                color = if (isSelected) Color.White else Color.Transparent,
                shape = CircleShape
            )
    )
}