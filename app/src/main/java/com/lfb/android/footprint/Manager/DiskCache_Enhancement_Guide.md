# 磁盘缓存增强功能指南

## 概述

为了解决首次启动时数据加载缓慢的问题，我们为 DataCacheManager 添加了磁盘缓存功能。现在系统支持两层缓存架构：

1. **第一层：内存缓存** - 快速访问，应用重启后丢失
2. **第二层：磁盘缓存** - 持久化存储，应用重启后仍然可用

## 新增功能

### 1. DiskCacheManager 类

新增的磁盘缓存管理器，提供以下功能：

- **持久化存储**: 数据存储在应用缓存目录中，重启后仍可用
- **JSON 序列化**: 使用 kotlinx.serialization 进行数据序列化
- **TTL 支持**: 磁盘缓存也支持过期时间
- **自动清理**: 当缓存大小超过限制时自动清理最旧的项
- **错误恢复**: 损坏的缓存文件会被自动删除

### 2. 增强的 DataCacheManager

原有的 DataCacheManager 现在支持：

- **两层缓存**: 内存缓存未命中时自动查询磁盘缓存
- **智能存储**: 只有特定类型的数据会被存储到磁盘
- **自动恢复**: 从磁盘缓存恢复的数据会自动加载到内存缓存

## 配置说明

### 磁盘缓存启用规则

在 `DataCacheManager.DiskCacheConfig` 中配置了哪些数据会使用磁盘缓存：

```kotlin
// 启用磁盘缓存的固定键
val DISK_CACHE_ENABLED_KEYS = setOf(
    CacheKeys.AVAILABLE_YEARS,      // 可用年份列表
    CacheKeys.LIFETIME_STATS,       // 一生数据统计
    CacheKeys.LAST_DATA_DAY         // 最后一天数据
)

// 启用磁盘缓存的前缀
val DISK_CACHE_ENABLED_PREFIXES = setOf(
    CacheKeys.YEAR_DAYS_PREFIX,     // 年份日期数据
    CacheKeys.YEAR_MONTHS_PREFIX,   // 年份月份数据
    CacheKeys.MONTH_DAYS_PREFIX,    // 月份日期数据
    CacheKeys.FIRST_DAY_YEAR_PREFIX // 年份首日数据
)
```

### TTL 配置

- **内存缓存 TTL**: 保持原有配置（30秒到1小时）
- **磁盘缓存 TTL**: 内存缓存 TTL × 10（5分钟到10小时）

## 使用方式

### 对于开发者

**无需修改现有代码！** 磁盘缓存功能是透明的：

```kotlin
// 现有代码无需修改，自动支持磁盘缓存
val years = realmManager.getAvailableYears()  // 自动使用两层缓存
val stats = realmManager.getLifetimeDataStats()  // 自动使用两层缓存
```

### 缓存工作流程

1. **数据查询**: 应用请求数据
2. **内存缓存检查**: 首先检查内存缓存
3. **磁盘缓存回退**: 内存缓存未命中时检查磁盘缓存
4. **数据库查询**: 两层缓存都未命中时查询数据库
5. **缓存存储**: 新数据同时存储到内存和磁盘缓存

### 缓存统计

获取详细的缓存使用情况：

```kotlin
val cacheManager = DataCacheManager.getInstance()
val stats = cacheManager.getCacheStats()

println("内存缓存: ${stats.validItems}/${stats.totalItems}")
stats.diskStats?.let { diskStats ->
    println("磁盘缓存: ${diskStats.validItems}/${diskStats.totalItems}")
    println("磁盘使用: ${diskStats.totalSizeBytes} bytes (${diskStats.usagePercentage}%)")
}
```

## 性能优化效果

### 首次启动优化

- **问题**: 首次启动时需要从数据库查询所有基础数据
- **解决**: 磁盘缓存保存了上次的查询结果，大幅减少数据库查询

### 具体优化的数据类型

1. **可用年份列表** (`getAvailableYears`)
   - 内存缓存: 5分钟
   - 磁盘缓存: 50分钟

2. **一生数据统计** (`getLifetimeDataStats`)
   - 内存缓存: 30秒
   - 磁盘缓存: 5分钟

3. **年份相关数据** (年份日期、月份等)
   - 内存缓存: 5分钟
   - 磁盘缓存: 50分钟

## 存储位置和管理

### 缓存文件位置

```
/data/data/com.lfb.android.footprint/cache/data_cache/
├── cache_metadata.json          # 缓存元数据
├── available_years.cache        # 可用年份缓存
├── lifetime_stats.cache         # 统计数据缓存
└── year_days_2023.cache        # 2023年日期缓存
```

### 自动清理机制

- **大小限制**: 最大 50MB
- **清理阈值**: 超过 40MB (80%) 时开始清理
- **清理策略**: 删除 25% 最久未访问的缓存项
- **过期清理**: 定期清理过期的缓存项

## 错误处理

### 磁盘缓存失败

如果磁盘缓存操作失败（如存储空间不足），系统会：

1. 记录错误日志
2. 继续使用内存缓存
3. 不影响正常的数据查询功能

### 缓存文件损坏

如果缓存文件损坏或无法读取：

1. 自动删除损坏的文件
2. 清理相关元数据
3. 重新从数据库查询数据

## 监控和调试

### 日志输出

系统会输出详细的缓存操作日志：

```
DataCache: Memory cache hit for key 'available_years'
DataCache: Disk cache hit for key 'lifetime_stats', restored to memory
DiskCache: Cached data for key 'year_days_2023' to disk (1024 bytes)
```

### 缓存清理日志

```
DataCache: Cleaned up 5 expired cache items
DiskCache: Cache size (45MB) exceeds threshold, starting cleanup
DiskCache: Cleaned up 25 oldest cache items
```

## 注意事项

1. **首次运行**: 第一次运行时磁盘缓存为空，需要正常查询数据库
2. **存储权限**: 使用应用缓存目录，无需额外权限
3. **数据一致性**: 数据变更时会同时清理内存和磁盘缓存
4. **性能影响**: 磁盘 I/O 在后台线程执行，不影响 UI 性能

## 未来扩展

可以考虑的进一步优化：

1. **压缩存储**: 对大型数据使用压缩算法
2. **加密存储**: 对敏感数据进行加密存储
3. **网络缓存**: 扩展到网络数据缓存
4. **缓存预热**: 应用启动时预加载常用数据

## 总结

磁盘缓存功能显著改善了应用的首次启动性能，特别是对于数据量较大的用户。通过两层缓存架构，我们在保持快速访问的同时，实现了数据的持久化存储，为用户提供更好的使用体验。
