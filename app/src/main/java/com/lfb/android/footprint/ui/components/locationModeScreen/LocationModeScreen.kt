package com.lfb.android.footprint.ui.components.locationModeScreen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.lfb.android.footprint.R
import com.lfb.android.footprint.location.LocationManager
import com.lfb.android.footprint.prefs.AppPrefs

@Composable
fun LocationModeScreen(
    locationManager: LocationManager,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 当前选中的模式状态
    var currentMode by remember { mutableStateOf(AppPrefs.sharedInstance.runningModel) }
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color(0xFF0F1419)) // 深蓝色背景，匹配设计稿
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp)
        ) {
            Spacer(modifier = Modifier.height(50.dp)) // 状态栏间距

            // 标题
            Text(
                text = "定位模式切换",
                color = Color.White,
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(20.dp))

            // 说明文字
            Text(
                text = "说明：目前支持省电、普通、耗电三种模式。",
                color = Color.White.copy(alpha = 0.8f),
                fontSize = 14.sp,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(30.dp))

            // 普通模式卡片
            LocationModeCard(
                title = "普通模式（推荐）",
                description = "默认为普通模式，满足日常轨迹的精度与耗电要求",
                batteryUsage = "平均耗电量大约为5%以下",
                accuracy = "轨迹密度大约是100米",
                isSelected = currentMode == 0,
                onClick = {
                    currentMode = 0
                    AppPrefs.sharedInstance.runningModel = 0
                    locationManager.startLocationUpdates()
                }
            )

            Spacer(modifier = Modifier.height(20.dp))

            // 省电模式卡片
            LocationModeCard(
                title = "省电模式",
                description = "省电模式是为满足对耗电有较高要求的用户",
                batteryUsage = "平均耗电量大约是这样：耗电1%以下",
                accuracy = "轨迹密度大约是200米，如下图记录的轨迹图：",
                isSelected = currentMode == 2,
                onClick = {
                    currentMode = 2
                    AppPrefs.sharedInstance.runningModel = 2
                    locationManager.startLocationUpdates()
                }
            )

            Spacer(modifier = Modifier.height(20.dp))

            // 耗电模式卡片
            LocationModeCard(
                title = "耗电模式",
                description = "耗电模式，满足用户对精度要求较高的需求",
                batteryUsage = "平均耗电量大约30%",
                accuracy = "轨迹密度大约是80米",
                isSelected = currentMode == 1,
                onClick = {
                    currentMode = 1
                    AppPrefs.sharedInstance.runningModel = 1
                    locationManager.startLocationUpdates()
                }
            )

            Spacer(modifier = Modifier.height(20.dp))

            // 更多详细使用说明
            Text(
                text = "更多详细使用说明",
                color = Color(0xFF4A90E2),
                fontSize = 14.sp,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { /* TODO: 跳转到详细说明 */ }
            )

            Spacer(modifier = Modifier.height(30.dp))

            // 关闭按钮
            Box(
                modifier = Modifier
                    .size(width = 120.dp, height = 40.dp)
                    .align(Alignment.CenterHorizontally)
                    .border(
                        width = 1.dp,
                        color = Color(0xFFD92D34),
                        shape = RoundedCornerShape(20.dp)
                    )
                    .clickable { onBackClick() },
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "关闭",
                    color = Color(0xFFD92D34),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
            }

            Spacer(modifier = Modifier.height(50.dp))
        }
    }
}

@Composable
private fun LocationModeCard(
    title: String,
    description: String,
    batteryUsage: String,
    accuracy: String,
    apps: List<AppInfo> = emptyList(),
    hasTrackImage: Boolean = false,
    isSelected: Boolean = false,
    onClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .background(
                color = Color(0xFF1E2A3A),
                shape = RoundedCornerShape(12.dp)
            )
            .border(
                width = if (isSelected) 1.dp else 0.dp,
                color = when {
                    isSelected -> Color(0xFFD92D34)
                    else -> Color(0xFF1E2A3A)
                },
                shape = RoundedCornerShape(12.dp)
            )
            .clickable { onClick() }
            .padding(16.dp)
    ) {
        Column {
            // 标题
            Text(
                text = title,
                color = Color.White,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 描述
            Text(
                text = description,
                color = Color.White.copy(alpha = 0.8f),
                fontSize = 14.sp,
                lineHeight = 20.sp
            )

            Spacer(modifier = Modifier.height(8.dp))

            // 耗电量
            Text(
                text = batteryUsage,
                color = Color.White.copy(alpha = 0.8f),
                fontSize = 14.sp,
                lineHeight = 20.sp
            )

            Spacer(modifier = Modifier.height(8.dp))

            // 精度
            Text(
                text = accuracy,
                color = Color.White.copy(alpha = 0.8f),
                fontSize = 14.sp,
                lineHeight = 20.sp
            )

            // 应用列表
            if (apps.isNotEmpty()) {
                Spacer(modifier = Modifier.height(16.dp))
                apps.forEach { app ->
                    AppUsageItem(app = app)
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }

            // 轨迹图片
            if (hasTrackImage) {
                Spacer(modifier = Modifier.height(16.dp))
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp)
                        .background(
                            color = Color.Black,
                            shape = RoundedCornerShape(8.dp)
                        )
                        .clip(RoundedCornerShape(8.dp))
                ) {
                    // 显示轨迹图片
//                    Image(
//                        painter = painterResource(id = R.drawable.track_sample),
//                        contentDescription = "轨迹图",
//                        contentScale = ContentScale.Fit,
//                        modifier = Modifier.fillMaxSize()
//                    )
                }
            }
        }
    }
}

@Composable
private fun AppUsageItem(
    app: AppInfo,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 应用图标
            Image(
                painter = painterResource(id = app.iconRes),
                contentDescription = app.name,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            Column {
                Text(
                    text = app.name,
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = app.status,
                    color = Color.White.copy(alpha = 0.6f),
                    fontSize = 12.sp
                )
            }
        }

        Text(
            text = app.batteryPercentage,
            color = Color.White.copy(alpha = 0.8f),
            fontSize = 14.sp
        )
    }
}

data class AppInfo(
    val name: String,
    val status: String,
    val batteryPercentage: String,
    val iconRes: Int
)
