package com.lfb.android.footprint

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.widget.Toast
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.*
import androidx.lifecycle.lifecycleScope
import com.lfb.android.footprint.ui.components.ImportExportScreen
import com.lfb.android.footprint.ui.components.ImportProgressDialog
import com.lfb.android.footprint.ui.components.ImportResultDialog
import com.lfb.android.footprint.ui.theme.ThemeUtils
import com.lfb.android.footprint.utils.CsvDataParser
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import android.util.Log

class ImportExportActivity : BaseActivity() {

    companion object {
        @Volatile
        var isActive = false
    }

    // 导入状态
    private var showImportProgress by mutableStateOf(false)
    private var importProgressMessage by mutableStateOf("")
    private var importProgress by mutableStateOf<Float?>(null)
    private var showImportResult by mutableStateOf(false)
    private var importResultSuccess by mutableStateOf(false)
    private var importResultTitle by mutableStateOf("")
    private var importResultMessage by mutableStateOf("")

    // 文件选择器启动器
    private val filePickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        Log.d("ImportExport", "文件选择器回调，URI: $uri")
        uri?.let {
            // 处理选中的文件
            Log.d("ImportExport", "开始处理选中的文件: $it")
            handleImportFile(it)
        } ?: run {
            Log.d("ImportExport", "用户取消了文件选择")
        }
    }

    // 文件保存启动器
    private val fileSaveLauncher = registerForActivityResult(
        ActivityResultContracts.CreateDocument("application/json")
    ) { uri: Uri? ->
        uri?.let {
            // 处理导出文件
            handleExportFile(it)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        // 根据用户的地图主题配置设置Activity主题
        setTheme(ThemeUtils.getActivityThemeForMapDisplayType(this))

        super.onCreate(savedInstanceState)
        Log.d("ImportExport", "onCreate called")

        setContent {
            ImportExportScreen(
                onBackClick = { finish() },
                onExportClick = {
                    // 启动文件保存选择器
                    fileSaveLauncher.launch("footprint_data.json")
                },
                onImportClick = {
                    // 启动文件选择器，支持多种文件类型
                    Log.d("ImportExport", "启动文件选择器")
                    filePickerLauncher.launch("*/*")
                }
            )

            // 导入进度对话框
            ImportProgressDialog(
                isVisible = showImportProgress,
                title = "正在导入数据",
                message = importProgressMessage,
                progress = importProgress
            )

            // 导入结果对话框
            ImportResultDialog(
                isVisible = showImportResult,
                success = importResultSuccess,
                title = importResultTitle,
                message = importResultMessage,
                onConfirm = {
                    showImportResult = false
                }
            )
        }
    }

    override fun onResume() {
        super.onResume()
        isActive = true
        Log.d("ImportExport", "onResume called")
    }

    override fun onPause() {
        super.onPause()
        Log.d("ImportExport", "onPause called")
    }

    override fun onDestroy() {
        super.onDestroy()
        isActive = false
        Log.d("ImportExport", "onDestroy called")
    }

    private fun handleImportFile(uri: Uri) {
        // 立即显示进度对话框
        Log.d("ImportExport", "显示进度对话框")
        showImportProgress = true
        importProgressMessage = "正在读取文件..."
        importProgress = 0.0f // 从0开始，避免null到0的突变

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // 创建CSV解析器
                val parser = CsvDataParser(this@ImportExportActivity)

                // 创建进度回调
                val progressCallback = object : CsvDataParser.ProgressCallback {
                    override suspend fun onProgress(
                        processedRows: Int,
                        totalRows: Int,
                        successRows: Int,
                        errorRows: Int,
                        duplicateRows: Int,
                        currentBatch: Int,
                        totalBatches: Int
                    ) {
                        withContext(Dispatchers.Main) {
                            val progress = if (totalRows > 0) {
                                processedRows.toFloat() / totalRows.toFloat()
                            } else null

                            Log.d("ImportExport", "进度回调: 已处理 $processedRows 行，进度 $progress")

                            importProgress = progress
                            importProgressMessage = buildString {
                                append("正在处理数据...\n")
                                append("已处理: $processedRows")
                                if (totalRows > 0) append(" / $totalRows")
                                append("\n")
                                append("成功: $successRows")
                                if (duplicateRows > 0) append(", 重复: $duplicateRows")
                                if (errorRows > 0) append(", 错误: $errorRows")
                                append("\n批次: $currentBatch")
                                if (totalBatches > 0) append(" / $totalBatches")
                            }
                        }
                    }

                    override suspend fun onBatchCompleted(batchNumber: Int, batchSize: Int) {
                        // 不更新UI消息，避免与onProgress冲突造成闪烁
                        // 批次完成信息已经在onProgress中显示
                    }
                }

                // 使用流式解析CSV文件
                withContext(Dispatchers.Main) {
                    importProgressMessage = "正在解析数据..."
                    importProgress = 0.0f // 设置初始进度为0
                    Log.d("ImportExport", "开始解析，进度对话框状态: $showImportProgress")
                }
                val parseResult = parser.parseCSVFileStreaming(uri, progressCallback)

                if (!parseResult.success) {
                    withContext(Dispatchers.Main) {
                        showImportProgress = false
                        showImportResultDialog(
                            success = false,
                            title = "导入失败",
                            message = parseResult.errorMessage ?: "未知错误"
                        )
                    }
                    return@launch
                }

                // 检查是否有有效数据（流式处理时data为空是正常的）
                if (parseResult.successRows == 0 && parseResult.duplicateRows == 0) {
                    withContext(Dispatchers.Main) {
                        showImportProgress = false
                        showImportResultDialog(
                            success = false,
                            title = "导入失败",
                            message = "文件中没有有效的GPS数据"
                        )
                    }
                    return@launch
                }

                withContext(Dispatchers.Main) {
                    // 流式处理已经完成数据库写入，这里只需要隐藏进度对话框
                    importProgressMessage = "导入完成"
                    importProgress = 1.0f

                    // 延迟1秒让用户看到完成状态，然后隐藏进度对话框
                    kotlinx.coroutines.delay(1000)
                    showImportProgress = false

                    // 显示导入结果
                    val message = buildString {
                        append("总行数: ${parseResult.totalRows}\n")
                        append("成功导入: ${parseResult.successRows} 条数据\n")
                        if (parseResult.duplicateRows > 0) {
                            append("跳过重复数据: ${parseResult.duplicateRows} 条\n")
                        }
                        if (parseResult.errorRows > 0) {
                            append("跳过无效数据: ${parseResult.errorRows} 条")
                        }
                    }

                    showImportResultDialog(
                        success = true,
                        title = "导入成功",
                        message = message
                    )
                }

            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    showImportProgress = false
                    showImportResultDialog(
                        success = false,
                        title = "导入失败",
                        message = "导入过程中发生错误: ${e.message}"
                    )
                }
            }
        }
    }

    private fun showImportResultDialog(success: Boolean, title: String, message: String) {
        importResultSuccess = success
        importResultTitle = title
        importResultMessage = message
        showImportResult = true
    }

    private fun handleExportFile(uri: Uri) {
        try {
            // TODO: 实现文件导出逻辑
            Toast.makeText(this, "导出位置: ${uri.path}", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Toast.makeText(this, "导出失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
}
