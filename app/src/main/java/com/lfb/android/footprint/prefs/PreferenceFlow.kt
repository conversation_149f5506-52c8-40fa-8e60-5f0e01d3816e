// prefs/PreferenceFlow.kt
package prefs

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map

object PreferenceFlow {
    val bus = MutableSharedFlow<Pair<String, Any?>>(replay = 0)

    /** 供 Preference 调用 */
    internal fun notifyChanged(key: String, value: Any?) {
        bus.tryEmit(key to value)
    }

    /** 监听某个 key 的值变化 */
    inline fun <reified T> observe(key: String): Flow<T> =
        bus.filter { it.first == key }
            .map { it.second as T }
}