package com.lfb.android.footprint.Manager

import android.content.Context
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.concurrent.ConcurrentHashMap

/**
 * 通用数据缓存管理器
 * 提供线程安全的缓存功能，支持TTL（生存时间）和LRU（最近最少使用）策略
 * 现在支持两层缓存：内存缓存（第一层）+ 磁盘缓存（第二层）
 */
class DataCacheManager private constructor(private val context: Context? = null) {

    companion object {
        @Volatile
        private var instance: DataCacheManager? = null

        fun getInstance(context: Context? = null): DataCacheManager {
            return instance ?: synchronized(this) {
                instance ?: DataCacheManager(context).also { instance = it }
            }
        }

        // 预定义的缓存键
        object CacheKeys {
            const val AVAILABLE_YEARS = "available_years"
            const val LAST_DATA_DAY = "last_data_day"
            const val LIFETIME_STATS = "lifetime_stats"
            const val YEAR_DAYS_PREFIX = "year_days_" // 后缀为年份
            const val DAY_DATA_PREFIX = "day_data_" // 后缀为 year_month_day
            const val DAY_COUNT_PREFIX = "day_count_" // 后缀为 year_month_day
            const val YEAR_MONTHS_PREFIX = "year_months_" // 后缀为年份
            const val MONTH_DAYS_PREFIX = "month_days_" // 后缀为 year_month
            const val FIRST_DAY_YEAR_PREFIX = "first_day_year_" // 后缀为年份
            const val NEXT_DAY_PREFIX = "next_day_" // 后缀为 year_month_day
        }

        // 预定义的缓存过期时间（毫秒）
        object CacheTTL {
            const val VERY_SHORT = 30_000L      // 30秒 - 用于频繁变化的数据
            const val SHORT = 60_000L           // 1分钟 - 用于一般查询数据
            const val MEDIUM = 300_000L         // 5分钟 - 用于相对稳定的数据
            const val LONG = 1_800_000L         // 30分钟 - 用于很少变化的数据
            const val VERY_LONG = 3_600_000L    // 1小时 - 用于基本不变的数据
        }

        // 辅助方法：生成组合键
        object KeyBuilder {
            fun yearDays(year: Int) = "${CacheKeys.YEAR_DAYS_PREFIX}$year"
            fun dayData(year: Int, month: Int, day: Int) = "${CacheKeys.DAY_DATA_PREFIX}${year}_${month}_$day"
            fun dayCount(year: Int, month: Int, day: Int) = "${CacheKeys.DAY_COUNT_PREFIX}${year}_${month}_$day"
            fun yearMonths(year: Int) = "${CacheKeys.YEAR_MONTHS_PREFIX}$year"
            fun monthDays(year: Int, month: Int) = "${CacheKeys.MONTH_DAYS_PREFIX}${year}_$month"
            fun firstDayYear(year: Int) = "${CacheKeys.FIRST_DAY_YEAR_PREFIX}$year"
            fun nextDay(year: Int, month: Int, day: Int) = "${CacheKeys.NEXT_DAY_PREFIX}${year}_${month}_$day"
        }
    }

    // 缓存项数据类
    private data class CacheItem<T>(
        val data: T,
        val timestamp: Long,
        val ttl: Long,
        var lastAccessed: Long = System.currentTimeMillis()
    ) {
        fun isExpired(): Boolean {
            return System.currentTimeMillis() - timestamp > ttl
        }

        fun isValid(): Boolean {
            return !isExpired()
        }

        fun updateAccess() {
            lastAccessed = System.currentTimeMillis()
        }
    }

    // 线程安全的缓存存储
    private val cache = ConcurrentHashMap<String, CacheItem<*>>()
    private val mutex = Mutex()

    // 最大缓存项数量（LRU策略）
    private val maxCacheSize = 1000

    // 磁盘缓存管理器（第二层缓存）
    private val diskCacheManager: DiskCacheManager? by lazy {
        context?.let { DiskCacheManager.getInstance(it) }
    }

    // 磁盘缓存配置
    object DiskCacheConfig {
        // 哪些类型的数据应该使用磁盘缓存
        val DISK_CACHE_ENABLED_KEYS = setOf(
            CacheKeys.AVAILABLE_YEARS,
            CacheKeys.LIFETIME_STATS,
            CacheKeys.LAST_DATA_DAY
        )

        // 哪些前缀的数据应该使用磁盘缓存
        val DISK_CACHE_ENABLED_PREFIXES = setOf(
            CacheKeys.YEAR_DAYS_PREFIX,
            CacheKeys.YEAR_MONTHS_PREFIX,
            CacheKeys.MONTH_DAYS_PREFIX,
            CacheKeys.FIRST_DAY_YEAR_PREFIX
        )

        // 磁盘缓存的TTL倍数（磁盘缓存保存更久）
        const val DISK_TTL_MULTIPLIER = 10
    }

    /**
     * 存储数据到缓存（内存 + 磁盘）
     * @param key 缓存键
     * @param data 要缓存的数据
     * @param ttl 生存时间（毫秒）
     */
    suspend fun <T> put(key: String, data: T, ttl: Long = CacheTTL.MEDIUM) {
        mutex.withLock {
            // 如果缓存已满，清理最旧的项
            if (cache.size >= maxCacheSize) {
                cleanupOldestItems()
            }

            // 存储到内存缓存
            cache[key] = CacheItem(data, System.currentTimeMillis(), ttl)
            println("DataCache: Cached data for key '$key' with TTL ${ttl}ms")

            // 如果启用了磁盘缓存，也存储到磁盘
            if (shouldUseDiskCache(key)) {
                try {
                    val diskTtl = ttl * DiskCacheConfig.DISK_TTL_MULTIPLIER
                    diskCacheManager?.put(key, data as Any, diskTtl)
                    println("DataCache: Also cached to disk for key '$key' with TTL ${diskTtl}ms")
                } catch (e: Exception) {
                    println("DataCache: Failed to cache to disk for key '$key': ${e.message}")
                }
            }
        }
    }

    /**
     * 从缓存获取数据（内存优先，磁盘回退）
     * @param key 缓存键
     * @return 缓存的数据，如果不存在或已过期则返回null
     */
    @Suppress("UNCHECKED_CAST")
    suspend fun <T> get(key: String): T? {
        mutex.withLock {
            val item = cache[key] as? CacheItem<T>

            // 首先尝试从内存缓存获取
            if (item != null && item.isValid()) {
                item.updateAccess()
                println("DataCache: Memory cache hit for key '$key'")
                return item.data
            }

            // 内存缓存失效，清理
            if (item != null) {
                cache.remove(key)
                println("DataCache: Memory cache expired for key '$key', removed")
            }

            // 如果启用了磁盘缓存，尝试从磁盘获取
            if (shouldUseDiskCache(key)) {
                try {
                    val diskData = getDiskCacheData<T>(key)
                    if (diskData != null) {
                        // 从磁盘缓存恢复到内存缓存
                        val currentTime = System.currentTimeMillis()
                        cache[key] = CacheItem(diskData, currentTime, CacheTTL.MEDIUM)
                        println("DataCache: Disk cache hit for key '$key', restored to memory")
                        return diskData
                    }
                } catch (e: Exception) {
                    println("DataCache: Failed to read from disk cache for key '$key': ${e.message}")
                }
            }

            println("DataCache: Complete cache miss for key '$key'")
            return null
        }
    }

    /**
     * 获取或计算数据（如果缓存中没有或已过期，则执行计算函数）
     * @param key 缓存键
     * @param ttl 生存时间（毫秒）
     * @param compute 计算函数
     * @return 缓存或计算的数据
     */
    suspend fun <T> getOrCompute(
        key: String, 
        ttl: Long = CacheTTL.MEDIUM, 
        compute: suspend () -> T
    ): T {
        // 先尝试从缓存获取
        val cached = get<T>(key)
        if (cached != null) {
            return cached
        }

        // 缓存中没有，执行计算
        println("DataCache: Computing data for key '$key'")
        val result = compute()
        
        // 将结果存入缓存
        put(key, result, ttl)
        
        return result
    }

    /**
     * 移除指定键的缓存（内存 + 磁盘）
     */
    suspend fun remove(key: String) {
        mutex.withLock {
            cache.remove(key)

            // 如果启用了磁盘缓存，也从磁盘删除
            if (shouldUseDiskCache(key)) {
                try {
                    diskCacheManager?.remove(key)
                } catch (e: Exception) {
                    println("DataCache: Failed to remove from disk cache for key '$key': ${e.message}")
                }
            }

            println("DataCache: Removed cache for key '$key'")
        }
    }

    /**
     * 移除匹配前缀的所有缓存项（内存 + 磁盘）
     */
    suspend fun removeByPrefix(prefix: String) {
        mutex.withLock {
            val keysToRemove = cache.keys.filter { it.startsWith(prefix) }
            keysToRemove.forEach { cache.remove(it) }

            // 如果启用了磁盘缓存，也从磁盘删除
            if (shouldUseDiskCacheForPrefix(prefix)) {
                try {
                    diskCacheManager?.removeByPrefix(prefix)
                } catch (e: Exception) {
                    println("DataCache: Failed to remove from disk cache with prefix '$prefix': ${e.message}")
                }
            }

            println("DataCache: Removed ${keysToRemove.size} cache items with prefix '$prefix'")
        }
    }

    /**
     * 清除所有缓存（内存 + 磁盘）
     */
    suspend fun clearAll() {
        mutex.withLock {
            val size = cache.size
            cache.clear()

            // 清除磁盘缓存
            try {
                diskCacheManager?.clearAll()
            } catch (e: Exception) {
                println("DataCache: Failed to clear disk cache: ${e.message}")
            }

            println("DataCache: Cleared all cache ($size items)")
        }
    }



    /**
     * 清理最旧的缓存项（LRU策略）
     */
    private fun cleanupOldestItems() {
        val itemsToRemove = cache.entries
            .sortedBy { it.value.lastAccessed }
            .take(maxCacheSize / 4) // 移除25%的最旧项
            .map { it.key }
        
        itemsToRemove.forEach { cache.remove(it) }
        println("DataCache: Cleaned up ${itemsToRemove.size} oldest cache items (LRU)")
    }

    /**
     * 获取缓存统计信息（内存 + 磁盘）
     */
    suspend fun getCacheStats(): CacheStats {
        mutex.withLock {
            val totalItems = cache.size
            val expiredItems = cache.values.count { it.isExpired() }
            val validItems = totalItems - expiredItems

            // 获取磁盘缓存统计
            val diskStats = try {
                diskCacheManager?.getCacheStats()
            } catch (e: Exception) {
                println("DataCache: Failed to get disk cache stats: ${e.message}")
                null
            }

            return CacheStats(
                totalItems = totalItems,
                validItems = validItems,
                expiredItems = expiredItems,
                maxSize = maxCacheSize,
                diskStats = diskStats
            )
        }
    }

    /**
     * 清理过期的缓存项（内存 + 磁盘）
     */
    suspend fun cleanupExpired() {
        mutex.withLock {
            val expiredKeys = cache.entries
                .filter { it.value.isExpired() }
                .map { it.key }

            expiredKeys.forEach { cache.remove(it) }

            // 清理磁盘缓存中的过期项
            try {
                diskCacheManager?.cleanupExpired()
            } catch (e: Exception) {
                println("DataCache: Failed to cleanup expired disk cache: ${e.message}")
            }

            if (expiredKeys.isNotEmpty()) {
                println("DataCache: Cleaned up ${expiredKeys.size} expired cache items")
            }
        }
    }

    // 辅助方法：判断是否应该使用磁盘缓存
    private fun shouldUseDiskCache(key: String): Boolean {
        if (diskCacheManager == null) return false

        // 检查是否在启用列表中
        if (DiskCacheConfig.DISK_CACHE_ENABLED_KEYS.contains(key)) {
            return true
        }

        // 检查是否匹配启用的前缀
        return DiskCacheConfig.DISK_CACHE_ENABLED_PREFIXES.any { prefix ->
            key.startsWith(prefix)
        }
    }

    // 辅助方法：判断前缀是否应该使用磁盘缓存
    private fun shouldUseDiskCacheForPrefix(prefix: String): Boolean {
        if (diskCacheManager == null) return false

        return DiskCacheConfig.DISK_CACHE_ENABLED_PREFIXES.any { enabledPrefix ->
            prefix.startsWith(enabledPrefix) || enabledPrefix.startsWith(prefix)
        }
    }

    // 辅助方法：从磁盘缓存获取特定类型的数据
    @Suppress("UNCHECKED_CAST")
    private suspend fun <T> getDiskCacheData(key: String): T? {
        return when (key) {
            CacheKeys.AVAILABLE_YEARS -> diskCacheManager?.getIntList(key) as? T
            CacheKeys.LAST_DATA_DAY -> diskCacheManager?.getTriple(key) as? T
            CacheKeys.LIFETIME_STATS -> diskCacheManager?.getStatsPair(key) as? T
            else -> {
                // 对于前缀匹配的键，根据前缀类型决定
                when {
                    key.startsWith(CacheKeys.YEAR_DAYS_PREFIX) -> diskCacheManager?.getIntList(key) as? T
                    key.startsWith(CacheKeys.YEAR_MONTHS_PREFIX) -> diskCacheManager?.getIntList(key) as? T
                    key.startsWith(CacheKeys.MONTH_DAYS_PREFIX) -> diskCacheManager?.getIntList(key) as? T
                    key.startsWith(CacheKeys.FIRST_DAY_YEAR_PREFIX) -> diskCacheManager?.getTriple(key) as? T
                    else -> null
                }
            }
        }
    }

    /**
     * 清除内存缓存（保留磁盘缓存）
     * 主要用于测试和调试
     */
    suspend fun clearMemoryCache() {
        mutex.withLock {
            val size = cache.size
            cache.clear()
            println("DataCache: Cleared memory cache ($size items), disk cache preserved")
        }
    }

    /**
     * 缓存统计信息数据类
     */
    data class CacheStats(
        val totalItems: Int,
        val validItems: Int,
        val expiredItems: Int,
        val maxSize: Int,
        val diskStats: DiskCacheManager.DiskCacheStats? = null
    )

}
