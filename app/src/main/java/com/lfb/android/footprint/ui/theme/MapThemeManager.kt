package com.lfb.android.footprint.ui.theme

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.Color
import com.lfb.android.footprint.prefs.AppPrefs

/**
 * 地图主题管理器 - 全局单例
 * 根据地图样式配置UI元素的主题色
 *
 * 规则：
 * - 通用地图(0)和卫星图(5)：黑色背景主题
 * - 其他地图样式：白色背景主题
 */
object MapThemeManager {

    // 全局响应式状态
    private var _currentMapDisplayType by mutableStateOf(AppPrefs.sharedInstance.mapDisplayType)

    /**
     * 获取当前地图显示类型
     */
    val currentMapDisplayType: Int
        get() = _currentMapDisplayType

    /**
     * 更新地图显示类型（会触发UI重组）
     */
    fun updateMapDisplayType(newType: Int) {
        _currentMapDisplayType = newType
        AppPrefs.sharedInstance.mapDisplayType = newType
    }

    /**
     * 获取当前主题配置
     */
    val currentThemeConfig: MapThemeConfig
        get() = getThemeConfig(_currentMapDisplayType)

    /**
     * 根据地图显示类型获取主题配置
     */
    fun getThemeConfig(mapDisplayType: Int): MapThemeConfig {
        return when (mapDisplayType) {
            0, 5 -> MapThemeConfig.DarkTheme  // 通用地图和卫星图
            else -> MapThemeConfig.LightTheme // 其他地图样式
        }
    }
}

/**
 * 地图主题配置数据类
 */
data class MapThemeConfig(
    val backgroundColor: Color,
    val backgroundColorAlpha90: Color,
    val backgroundColorAlpha80: Color,
    val backgroundColorAlpha70: Color,
    val textColor: Color,
    val textColorSecondary: Color,
    val isDarkTheme: Boolean
) {
    companion object {
        /**
         * 黑色主题配置（通用地图、卫星图）
         */
        val DarkTheme = MapThemeConfig(
            backgroundColor = Color(0xFF171919).copy(alpha = 0.9f),
            backgroundColorAlpha90 = Color.Black.copy(alpha = 0.9f),
            backgroundColorAlpha80 = Color.Black.copy(alpha = 0.8f),
            backgroundColorAlpha70 = Color.Black.copy(alpha = 0.7f),
//            textColor = Color.White.copy(alpha = 0.8f),
            textColor = Color(0xFF4A90E2).copy(alpha = 0.8f),
            textColorSecondary = Color.White.copy(alpha = 0.7f),
            isDarkTheme = true
        )
        
        /**
         * 白色主题配置（其他地图样式）
         */
        val LightTheme = MapThemeConfig(
            backgroundColor = Color.White,
            backgroundColorAlpha90 = Color.White.copy(alpha = 0.9f),
            backgroundColorAlpha80 = Color.White.copy(alpha = 0.8f),
            backgroundColorAlpha70 = Color.White.copy(alpha = 0.7f),
            textColor = Color.Black,
            textColorSecondary = Color.Black.copy(alpha = 0.8f),
            isDarkTheme = false
        )
    }
}

/**
 * Composable函数：获取当前地图主题配置（响应式）
 * 这个函数会自动响应全局主题状态的变化
 */
@Composable
fun rememberMapThemeConfig(): MapThemeConfig {
    val mapDisplayType = MapThemeManager.currentMapDisplayType
    return remember(mapDisplayType) {
        MapThemeManager.getThemeConfig(mapDisplayType)
    }
}

/**
 * Composable函数：获取指定地图显示类型的主题配置（向后兼容）
 */
@Composable
fun rememberMapThemeConfig(mapDisplayType: Int): MapThemeConfig {
    return remember(mapDisplayType) {
        MapThemeManager.getThemeConfig(mapDisplayType)
    }
}

/**
 * 扩展函数：为现有颜色应用主题
 */
fun Color.applyMapTheme(themeConfig: MapThemeConfig): Color {
    return if (themeConfig.isDarkTheme) {
        // 如果是深色主题，将浅色转换为深色
        when (this) {
            Color.White -> Color.Black
            Color.Black -> Color.White
            else -> this
        }
    } else {
        // 如果是浅色主题，保持原色或将深色转换为浅色
        when (this) {
            Color.Black -> Color.White
            else -> this
        }
    }
}
