package com.lfb.android.footprint.ui.components.mapScreen

import android.annotation.SuppressLint
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.lfb.android.footprint.ui.theme.mainRedColor
import com.lfb.android.footprint.ui.theme.rememberMapThemeConfig
import com.lfb.android.footprint.prefs.AppPrefs
import androidx.compose.foundation.background
import androidx.compose.foundation.shape.RoundedCornerShape

@SuppressLint("DefaultLocale")
@Composable
fun MapDataInfoCard (
    dataCount: Int,
    dataDistance: Double,
    selectedIndex: Int,
    modifier: Modifier = Modifier
) {
    val themeConfig = rememberMapThemeConfig()
    Box(
        modifier = modifier
//            .background(
//                color = themeConfig.backgroundColorAlpha90,
//                shape = RoundedCornerShape(12.dp)
//            )
    ) {

        // 文字内容和背景
        Row(
            modifier = Modifier
                .padding(8.dp)
                .padding(start = 12.dp, end = 12.dp, top = 8.dp, bottom = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = buildAnnotatedString {
                        if (selectedIndex == 0) {
                            append("此生")
                        } else if (selectedIndex == 1) {
                            append("当前")
                        } else if (selectedIndex == 2) {
                            append("今日")
                        } else if (selectedIndex == 3) {
                            append("昨日")
                        } else if (selectedIndex == 4) {
                            append("七日")
                        }

                        append("已记录")
                        withStyle(style = SpanStyle(color = mainRedColor)) { // 设置 dataCount 的颜色
                            append(" $dataCount ")
                        }
                        append("个轨迹点")
                    },
                    style = TextStyle(
                        fontSize = 13.sp,
                        fontWeight = FontWeight.Bold, // Add this line to make the font bold
                        color = themeConfig.textColor // 使用主题颜色
                    )
                )
                Spacer(modifier = Modifier.padding(top = 8.dp)) // 设置你想要的间距
                Text(
                    text = buildAnnotatedString {
                        append("总距离")

                        if (dataDistance > 1000) {
                            withStyle(style = SpanStyle(color = mainRedColor)) { // 设置 dataCount 的颜色
                                append(String.format(" %.2f ", dataDistance / 1000)) // 保留两位小数
                            }

                            append("千米")
                        } else {
                            withStyle(style = SpanStyle(color = mainRedColor)) { // 设置 dataCount 的颜色
                                append(String.format(" %.2f ", dataDistance)) // 保留两位小数
                            }
                            append("米")
                        }
                    },
                    style = TextStyle(
                        fontSize = 13.sp,
                        fontWeight = FontWeight.Bold, // Add this line to make the font bold
                        color = themeConfig.textColor // 使用主题颜色
                    )
                )
            }
        }

    }
}