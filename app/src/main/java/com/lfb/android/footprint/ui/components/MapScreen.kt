package com.lfb.android.footprint.ui.components

import android.content.Intent
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import com.mapbox.geojson.Point
import com.mapbox.maps.CameraOptions
import com.lfb.android.footprint.R
import com.lfb.android.footprint.SettingsActivity
import com.lfb.android.footprint.location.LocationManager
import com.lfb.android.footprint.location.LocationDataRecorder
import com.lfb.android.footprint.model.StepDataRealmModel
import com.lfb.android.footprint.prefs.AppPrefs
import com.lfb.android.footprint.ui.components.mapScreen.MapContent
import com.lfb.android.footprint.ui.components.mapScreen.DrawPointAnimationType
import com.lfb.android.footprint.ui.components.mapScreen.AppHeader
import com.lfb.android.footprint.ui.components.mapScreen.MapCurrentStateInfoCard
//import com.lfb.android.footprint.ui.components.mapScreen.MapDatasInfoCard
import com.lfb.android.footprint.ui.components.mapScreen.MapControlButtons
import com.lfb.android.footprint.ui.components.mapScreen.BottomControlBar
import com.lfb.android.footprint.ui.components.mapScreen.ConfigPanel
import com.lfb.android.footprint.ui.components.mapScreen.CustomTimeRangeSelector
import com.lfb.android.footprint.ui.components.mapScreen.calculateTotalDistance
import com.mapbox.maps.extension.compose.animation.viewport.rememberMapViewportState
import kotlinx.coroutines.*
import kotlinx.datetime.*
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.sp
import com.lfb.android.footprint.Manager.RealmModelManager
import com.lfb.android.footprint.ui.components.mapScreen.MapDataInfoCard
import kotlin.coroutines.coroutineContext

@Composable
fun MapScreen(
    locationManager: LocationManager,
    locationDataRecorder: LocationDataRecorder,
    showRecordModeDialog: (callback: (Int) -> Unit) -> Unit
) {
    // 地图视口状态
    val mapViewportState = rememberMapViewportState {
        val point = Point.fromLngLat(
            AppPrefs.sharedInstance.lastLocationlongitude,
            AppPrefs.sharedInstance.lastLocationlatitude
        )
        setCameraOptions {
            center(point)
            zoom(15.0)
        }
    }

    // 核心状态
    val currentLocation = locationManager.locationFlow.collectAsState(initial = locationManager.lastLocation).value
    var runningModel by remember { mutableStateOf(AppPrefs.sharedInstance.runningModel) }
    var selectedFilter by remember { mutableStateOf(2) }
    var showLocationToast by remember { mutableStateOf(false) }
    var mapStyleLoaded by remember { mutableStateOf(false) }

    // 轨迹相关状态
    var trackPoints by remember { mutableStateOf(mutableListOf<Point>()) }
    var trackDrawVersion by remember { mutableStateOf(0) }
    var drawPointAnimationType by remember { mutableStateOf(DrawPointAnimationType.FREE_MODE) }
    var detailTrackDrawVersion by remember { mutableStateOf(0) }

    // 面板状态
    var showConfigPanel by remember { mutableStateOf(false) }
    var showDetailPanel by remember { mutableStateOf(false) }
    var showCustomTimeSelector by remember { mutableStateOf(false) }

    // 地图配置状态 - 使其响应式
    var mapDisplayType by remember { mutableStateOf(AppPrefs.sharedInstance.mapDisplayType) }
    var mapShowAddressName by remember { mutableStateOf(AppPrefs.sharedInstance.mapShowAdressName) }
    var mapDrawLineAlpha by remember { mutableStateOf(AppPrefs.sharedInstance.mapDrawLineAlpha) }
    var mapDrawSpotAlpha by remember { mutableStateOf(AppPrefs.sharedInstance.mapDrawSpotAlpha) }
    var mapDrawLineWidth by remember { mutableStateOf(AppPrefs.sharedInstance.mapDrawLineWidth) }
    var mapDrawLineColor by remember { mutableStateOf(AppPrefs.sharedInstance.mapDrawLineColor) }

    // 自定义时间范围状态 - 默认结束时间为今日，开始时间为一个月前
    val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
    val defaultEndTime = (now.plus(DatePeriod(days = 1))).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds
    val defaultStartTime = (now.minus(DatePeriod(months = 1))).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds

    var customStartTime by remember { mutableStateOf(defaultStartTime) }
    var customEndTime by remember { mutableStateOf(defaultEndTime) }

    // 详细面板相关状态
    var detailPanelTrackPoints by remember { mutableStateOf<List<Point>>(emptyList()) }
    var selectedDataPoint by remember { mutableStateOf<Point?>(null) }
    var selectedDataPointInfo by remember { mutableStateOf<StepDataRealmModel?>(null) }
    var detailPanelHeight by remember { mutableStateOf(600) }
    var preDeleteDataPoints by remember { mutableStateOf<List<StepDataRealmModel>>(emptyList()) }
    var originalDayDataPoints by remember { mutableStateOf<List<StepDataRealmModel>>(emptyList()) }

    // 一生模式相关状态
    var lifetimeDataPoints by remember { mutableStateOf<List<Point>>(emptyList()) }
    var lifetimeDataVersion by remember { mutableStateOf(0) }
    var currentMapBounds by remember { mutableStateOf<Pair<Pair<Double, Double>, Pair<Double, Double>>?>(null) }
    var lastLoadedBounds by remember { mutableStateOf<Pair<Pair<Double, Double>, Pair<Double, Double>>?>(null) }
    var lastLoadedZoom by remember { mutableStateOf<Double?>(null) }
    var isLoadingLifetimeData by remember { mutableStateOf(false) }
    var pendingViewportUpdate by remember { mutableStateOf<(() -> Unit)?>(null) }
    var lifetimeTotalDataCount by remember { mutableStateOf(0L) }
    var lifetimeTotalDistance by remember { mutableStateOf(0.0) }

    // 取消机制相关状态
    var currentDataLoadingJob by remember { mutableStateOf<Job?>(null) }
    var currentLoadingTaskId by remember { mutableStateOf(0L) }

    // 获取屏幕密度和尺寸
    val density = LocalDensity.current.density
    val context = LocalContext.current
    val displayMetrics = context.resources.displayMetrics
    val screenHeightPx = displayMetrics.heightPixels
    val screenHeight = screenHeightPx.toDouble()

    val coroutineScope = rememberCoroutineScope()

    // 地图视口变化处理函数（带防抖机制）
    val handleViewportChanged = { minLat: Double, maxLat: Double, minLng: Double, maxLng: Double, screenWidth: Int, screenHeight: Int, zoomLevel: Double ->
        if (selectedFilter == 0 && !isLoadingLifetimeData) {
            // 检查是否需要重新加载数据
            val shouldReload = lastLoadedBounds?.let { lastBounds ->
                // 检查缩放级别变化
                val zoomChanged = lastLoadedZoom?.let { lastZoom ->
                    kotlin.math.abs(zoomLevel - lastZoom) >= 1.0 // 缩放级别变化超过1级
                } ?: true

                if (zoomChanged) {
                    true // 缩放级别变化超过1级，需要重新加载
                } else {
                    // 计算当前视口中心点
                    val currentCenterLat = (minLat + maxLat) / 2
                    val currentCenterLng = (minLng + maxLng) / 2

                    // 计算上次加载的视口中心点
                    val lastCenterLat = (lastBounds.first.first + lastBounds.first.second) / 2
                    val lastCenterLng = (lastBounds.second.first + lastBounds.second.second) / 2

                    // 计算移动距离（以视口大小的比例计算）
                    val latRange = maxLat - minLat
                    val lngRange = maxLng - minLng
                    val latMovement = kotlin.math.abs(currentCenterLat - lastCenterLat) / latRange
                    val lngMovement = kotlin.math.abs(currentCenterLng - lastCenterLng) / lngRange

                    // 如果移动距离超过半个屏幕，则需要重新加载
                    latMovement > 0.5 || lngMovement > 0.5
                }
            } ?: true // 如果是第一次加载，则需要加载

            if (shouldReload) {
                // 取消之前的待处理更新
                pendingViewportUpdate?.let {
                    pendingViewportUpdate = null
                }

                // 创建新的待处理更新
                val updateAction = {
                    if (!isLoadingLifetimeData) {
                        coroutineScope.launch {
                            try {
                                isLoadingLifetimeData = true
                                val realmManager = RealmModelManager.getInstance()

                                // 计算扩展后的边界（增加半个屏幕的缓冲区）
                                val latRange = maxLat - minLat
                                val lngRange = maxLng - minLng
                                val bufferLat = latRange * 0.5 // 半个屏幕的纬度范围
                                val bufferLng = lngRange * 0.5 // 半个屏幕的经度范围

                                val expandedMinLat = minLat - bufferLat
                                val expandedMaxLat = maxLat + bufferLat
                                val expandedMinLng = minLng - bufferLng
                                val expandedMaxLng = maxLng + bufferLng

                                lifetimeDataPoints = realmManager.getLifetimeDataPointsForViewport(
                                    taskId = 0,
                                    minLat = expandedMinLat,
                                    maxLat = expandedMaxLat,
                                    minLng = expandedMinLng,
                                    maxLng = expandedMaxLng,
                                    screenWidthPx = screenWidth,
                                    screenHeightPx = screenHeight
                                )

                                println("lifetimeDataPointsCount: ${lifetimeDataPoints.count()}")

                                lifetimeDataVersion++

                                // 更新最后加载的边界和缩放级别
                                lastLoadedBounds = Pair(Pair(minLat, maxLat), Pair(minLng, maxLng))
                                lastLoadedZoom = zoomLevel

                            } catch (e: Exception) {
                                println("Failed to update lifetime data for viewport: ${e.message}")
                            } finally {
                                isLoadingLifetimeData = false
                                pendingViewportUpdate = null
                            }
                        }
                    }
                }

                pendingViewportUpdate = updateAction

                // 延迟执行，实现防抖效果
                coroutineScope.launch {
                    delay(300) // 300ms防抖延迟
                    if (pendingViewportUpdate == updateAction) {
                        updateAction()
                    }
                }
            }
        }
    }

    // 过滤器变化监听
    LaunchedEffect(selectedFilter) {
        // 取消正在进行的数据加载任务
        currentDataLoadingJob?.cancel()
        currentDataLoadingJob = null
        currentLoadingTaskId++ // 使所有正在进行的任务失效
        println("lifetime: Filter changed to $selectedFilter, cancelled all tasks, new taskId: $currentLoadingTaskId")

        trackPoints.clear()
        lifetimeDataPoints = emptyList()
        lastLoadedBounds = null // 重置加载边界
        lastLoadedZoom = null // 重置缩放级别
        isLoadingLifetimeData = false // 重置加载状态
        pendingViewportUpdate = null // 重置待处理更新
        lifetimeTotalDataCount = 0L // 重置一生数据统计
        lifetimeTotalDistance = 0.0
        when (selectedFilter) {
            0 -> {
                // 一生模式 - 按地图比例过滤数据点
                showCustomTimeSelector = false
                println("lifetime: Entering lifetime mode")

                // 生成新的任务ID
                val taskId = ++currentLoadingTaskId
                println("lifetime: Initial data loading task ID: $taskId")

                val job = coroutineScope.launch {
                    try {
                        val realmManager = RealmModelManager.getInstance()

                        // 获取所有数据的边界框
                        val bounds = realmManager.getAllDataBounds()
                        if (bounds != null) {
                            currentMapBounds = bounds
                            println("lifetime: Task $taskId - Got data bounds: lat[${bounds.first.first}, ${bounds.first.second}], lng[${bounds.second.first}, ${bounds.second.second}]")

                            // 按当前屏幕尺寸过滤数据点，初始加载使用完整边界
                            val screenWidthPx = displayMetrics.widthPixels
                            val screenHeightPx = displayMetrics.heightPixels
                            println("lifetime: Task $taskId - Screen dimensions: ${screenWidthPx}x${screenHeightPx}")

                            // 清空现有数据，准备接收新的批量数据
                            lifetimeDataPoints = emptyList()
                            val allBatchPoints = mutableListOf<Point>()

                            realmManager.getLifetimeDataPointsForViewportBatch(
                                taskId = taskId.toInt(),
                                minLat = bounds.first.first,
                                maxLat = bounds.first.second,
                                minLng = bounds.second.first,
                                maxLng = bounds.second.second,
                                screenWidthPx = screenWidthPx,
                                screenHeightPx = screenHeightPx,
                                onBatchReady = { batchPoints ->
                                    // 检查任务是否仍然有效
                                    if (currentLoadingTaskId == taskId) {
                                        // 每收到一批数据就立即更新UI
                                        allBatchPoints.addAll(batchPoints)
                                        lifetimeDataPoints = allBatchPoints.toList()
                                        lifetimeDataVersion++
                                        println("lifetime: Task $taskId - Initial batch received: ${batchPoints.size} points, total: ${allBatchPoints.size}")
                                    } else {
                                        println("lifetime: Task $taskId - Initial batch ignored: taskId mismatch (current: $currentLoadingTaskId, expected: $taskId)")
                                    }
                                },
                                isCancelled = {
                                    val cancelled = currentLoadingTaskId != taskId
                                    if (cancelled) {
                                        println("lifetime: Task $taskId - Initial cancellation check: taskId mismatch (current: $currentLoadingTaskId, expected: $taskId)")
                                    }
                                    cancelled
                                }
                            )

                            // 只有在任务仍然有效时才更新最终状态
                            if (currentLoadingTaskId == taskId) {
                                println("lifetime: Task $taskId - Initial loading completed, total points: ${lifetimeDataPoints.count()}")

                                // 设置初始加载边界为完整边界
                                lastLoadedBounds = bounds

                                // 获取一生数据统计
                                val (totalCount, totalDistance, _) = realmManager.getLifetimeDataStatsCompat()
                                lifetimeTotalDataCount = totalCount
                                lifetimeTotalDistance = totalDistance
                                println("lifetime: Task $taskId - Stats: $totalCount total records, ${totalDistance}km total distance")
                            } else {
                                println("lifetime: Task $taskId - Initial loading completion ignored: taskId mismatch (current: $currentLoadingTaskId)")
                            }
                        } else {
                            println("lifetime: Task $taskId - No data bounds available")
                        }
                    } catch (e: Exception) {
                        if (e !is CancellationException) {
                            println("lifetime: Task $taskId - Failed to load initial data: ${e.message}")
                        } else {
                            println("lifetime: Task $taskId - Initial loading cancelled via exception")
                        }
                    } finally {
                        // 只有在当前任务仍然有效时才重置状态
                        if (currentLoadingTaskId == taskId) {
                            println("lifetime: Task $taskId - Cleaning up initial loading task")
                            currentDataLoadingJob = null
                        } else {
                            println("lifetime: Task $taskId - Initial cleanup skipped: taskId mismatch (current: $currentLoadingTaskId)")
                        }
                    }
                }

                currentDataLoadingJob = job
            }
            1 -> {
                // 自定义时间范围 - 显示时间选择控件并加载默认时间范围的数据
                showCustomTimeSelector = true
                trackPoints.addAll(RealmModelManager.getInstance().getStepDataByTimeRange(customStartTime, customEndTime).convertStepDataToPoints())
            }
            2 -> {
                showCustomTimeSelector = false
                trackPoints.addAll(RealmModelManager.getInstance().queryTodayData<StepDataRealmModel>().convertStepDataToPoints())
            }
            3 -> {
                showCustomTimeSelector = false
                trackPoints.addAll(RealmModelManager.getInstance().queryYesterdayData<StepDataRealmModel>().convertStepDataToPoints())
            }
            4 -> {
                showCustomTimeSelector = false
                trackPoints.addAll(RealmModelManager.getInstance().queryWeekAllData<StepDataRealmModel>().convertStepDataToPoints())
            }
        }

        // 非一生模式才更新trackDrawVersion
        if (selectedFilter != 0) {
            trackDrawVersion++
            drawPointAnimationType = DrawPointAnimationType.SCALE_MODE
        }
    }

    // 自定义时间范围变化监听（仅在用户手动修改时间时触发）
    LaunchedEffect(customStartTime, customEndTime) {
        // 只有在自定义模式下且时间范围发生变化时才重新加载数据
        if (selectedFilter == 1 && customStartTime > 0 && customEndTime > 0) {
            trackPoints.clear()
            trackPoints.addAll(RealmModelManager.getInstance().getStepDataByTimeRange(customStartTime, customEndTime).convertStepDataToPoints())
            trackDrawVersion++
            drawPointAnimationType = DrawPointAnimationType.SCALE_MODE
        }
    }

    // 位置更新监听
    LaunchedEffect(currentLocation) {
        currentLocation?.let {
            if (selectedFilter != 2) return@let
            val newPoint = Point.fromLngLat(currentLocation.longitude, currentLocation.latitude)
            mapViewportState.flyTo(
                CameraOptions.Builder().center(newPoint).zoom(14.0).build()
            )
            trackPoints.add(newPoint)
            trackDrawVersion++
            coroutineScope.launch {
                delay(300)
                showLocationToast = true
            }
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // 地图内容
        MapContent(
            mapViewportState = mapViewportState,
            trackPoints = trackPoints,
            trackDrawVersion = trackDrawVersion,
            detailPanelTrackPoints = detailPanelTrackPoints,
            detailTrackDrawVersion = detailTrackDrawVersion,
            selectedDataPoint = selectedDataPoint,
            selectedDataPointInfo = selectedDataPointInfo,
            showDetailPanel = showDetailPanel,
            drawPointAnimationType = drawPointAnimationType,
            detailPanelHeight = detailPanelHeight,
            screenHeight = screenHeight,
            // 一生模式相关参数
            selectedFilter = selectedFilter,
            lifetimeDataPoints = lifetimeDataPoints,
            lifetimeDataVersion = lifetimeDataVersion,
            // 地图配置参数
            mapDisplayType = mapDisplayType,
            mapShowAddressName = mapShowAddressName,
            mapDrawLineAlpha = mapDrawLineAlpha,
            mapDrawSpotAlpha = mapDrawSpotAlpha,
            mapDrawLineWidth = mapDrawLineWidth,
            mapDrawLineColor = mapDrawLineColor,
            onViewportChanged = handleViewportChanged,
            onMapStyleLoaded = { mapStyleLoaded = true },
            onDrawPointAnimationTypeChanged = { drawPointAnimationType = it }
        )

        // App Header
        AppHeader(
            gpsSignal = currentLocation?.accuracy?.toInt() ?: 0,
            runningModel = runningModel,
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(start = 16.dp, top = 38.dp)
                .clickable {
                    showRecordModeDialog { selectedMode ->
                        AppPrefs.sharedInstance.runningModel = selectedMode
                        runningModel = selectedMode
                        locationManager.startLocationUpdates()
                    }
                }
        )

        // 当前状态信息卡片
        MapCurrentStateInfoCard(
            altitude = currentLocation?.altitude?.toFloat() ?: 0f,
            speed = currentLocation?.speed ?: 0f,
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(start = 16.dp, top = 88.dp)
        )

        // 数据信息卡片
        MapDataInfoCard(
            dataCount = if (selectedFilter == 0) lifetimeTotalDataCount.toInt() else trackPoints.count(),
            dataDistance = if (selectedFilter == 0) lifetimeTotalDistance else calculateTotalDistance(trackPoints).toDouble(),
            selectedIndex = selectedFilter,
            modifier = Modifier
                .align(Alignment.BottomStart)
                .padding(end = 12.dp, bottom = 128.dp)
        )

        // 地图控制按钮
        MapControlButtons(
            onLocationClick = {
                when (drawPointAnimationType) {
                    DrawPointAnimationType.FREE_MODE, DrawPointAnimationType.SCALE_MODE -> {
                        drawPointAnimationType = DrawPointAnimationType.FOLLOW_MODE
                        locationManager.lastLocation?.let {
                            val newPoint = Point.fromLngLat(it.longitude, it.latitude)
                            mapViewportState.flyTo(
                                CameraOptions.Builder().center(newPoint)
                                    .bearing(0.0)
                                    .zoom(15.0).build()
                            )
                        }
                    }
                    DrawPointAnimationType.FOLLOW_MODE -> {
                        drawPointAnimationType = DrawPointAnimationType.SCALE_MODE
                    }
                }
            },
            onConfigClick = { showConfigPanel = true },
            onDetailClick = { showDetailPanel = true },
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(end = 12.dp, bottom = 108.dp)
        )

        // 底部控制栏
        BottomControlBar(
            onFilterSelected = { selectedFilter = it },
            onLeftButtonClick = {
                // 跳转到设置页面
                val intent = Intent(context, SettingsActivity::class.java)
                context.startActivity(intent)
            },
            onRightButtonClick = { },
            leftIconResId = R.drawable.menu,
            rightIconResId = R.drawable.overview,
            modifier = Modifier.align(Alignment.BottomCenter)
        )

        // 配置面板
        if (showConfigPanel) {
            ConfigPanel(
                onClose = { showConfigPanel = false },
                onMapDisplayTypeChanged = { mapDisplayType = it },
                onMapShowAddressNameChanged = { mapShowAddressName = it },
                onMapDrawLineAlphaChanged = { mapDrawLineAlpha = it },
                onMapDrawSpotAlphaChanged = { mapDrawSpotAlpha = it },
                onMapDrawLineWidthChanged = { mapDrawLineWidth = it },
                onMapDrawLineColorChanged = { mapDrawLineColor = it }
            )
        }

        // 详细数据面板
        if (showDetailPanel) {
            DetailDataPanel(
                onClose = {
                    showDetailPanel = false
                    detailPanelTrackPoints = emptyList()
                    selectedDataPoint = null
                    selectedDataPointInfo = null
                    preDeleteDataPoints = emptyList()
                    detailTrackDrawVersion++
                },
                onDayDataChanged = { dayDataPoints ->
                    originalDayDataPoints = dayDataPoints
                    detailPanelTrackPoints = dayDataPoints.convertStepDataToPoints()
                    detailTrackDrawVersion++
                    selectedDataPoint = null
                    selectedDataPointInfo = null
                },
                onDataPointSelected = { dataPoint ->
                    selectedDataPoint = Point.fromLngLat(dataPoint.longitude, dataPoint.latitude)
                    selectedDataPointInfo = dataPoint
                    detailTrackDrawVersion++
                },
                onHeightChanged = { heightInDp ->
                    detailPanelHeight = heightInDp
                },
                onPreDeleteDataPoints = { dataPointsToDelete ->
                    preDeleteDataPoints = dataPointsToDelete
                    val deleteDataTimes = dataPointsToDelete.map { it.dataTime }.toSet()
                    val remainingDataPoints = originalDayDataPoints.filter { dataPoint ->
                        !deleteDataTimes.contains(dataPoint.dataTime)
                    }
                    detailPanelTrackPoints = remainingDataPoints.convertStepDataToPoints()
                    selectedDataPoint = null
                    selectedDataPointInfo = null
                    detailTrackDrawVersion++
                },
                onCancelPreDelete = {
                    preDeleteDataPoints = emptyList()
                    detailPanelTrackPoints = originalDayDataPoints.convertStepDataToPoints()
                    detailTrackDrawVersion++
                },
                onConfirmDelete = { deletedDataPoints ->
                    preDeleteDataPoints = emptyList()
                    detailTrackDrawVersion++
                }
            )
        }

        // 自定义时间选择控件
        if (showCustomTimeSelector) {
            CustomTimeRangeSelector(
                startTime = customStartTime,
                endTime = customEndTime,
                onTimeRangeChanged = { startTime, endTime ->
                    customStartTime = startTime
                    customEndTime = endTime
                },
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(start = 16.dp, bottom = 520.dp)
            )
        }


    }
}



// 扩展函数：将StepDataRealmModel列表转换为Point列表
fun List<StepDataRealmModel>.convertStepDataToPoints(): List<Point> {
    return this.map { Point.fromLngLat(it.longitude, it.latitude) }
}
