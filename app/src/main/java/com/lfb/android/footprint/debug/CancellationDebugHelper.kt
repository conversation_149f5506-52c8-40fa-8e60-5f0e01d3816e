package com.lfb.android.footprint.debug

import kotlinx.coroutines.*

/**
 * 调试取消机制的辅助工具
 */
object CancellationDebugHelper {
    
    /**
     * 测试取消机制是否正常工作
     */
    suspend fun testCancellationMechanism() {
        var taskId = 0L
        var processedCount = 0
        
        println("=== 开始测试取消机制 ===")
        
        // 模拟第一个任务
        val firstTaskId = ++taskId
        val firstJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                repeat(10000) { i ->
                    // 模拟取消检查
                    if (taskId != firstTaskId) {
                        println("第一个任务在第 $i 次迭代时被取消 (taskId: $firstTaskId, current: $taskId)")
                        return@launch
                    }
                    processedCount++
                    delay(1) // 模拟处理时间
                }
                println("第一个任务完成，处理了 $processedCount 条数据")
            } catch (e: CancellationException) {
                println("第一个任务被协程取消")
            }
        }
        
        // 让第一个任务运行一段时间
        delay(50)
        
        // 启动第二个任务（模拟新的视口变化）
        val secondTaskId = ++taskId
        println("启动第二个任务，taskId: $secondTaskId，第一个任务应该被取消")
        
        val secondJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                repeat(1000) { i ->
                    if (taskId != secondTaskId) {
                        println("第二个任务在第 $i 次迭代时被取消")
                        return@launch
                    }
                    delay(1)
                }
                println("第二个任务完成")
            } catch (e: CancellationException) {
                println("第二个任务被协程取消")
            }
        }
        
        // 等待任务完成
        joinAll(firstJob, secondJob)
        
        println("第一个任务处理了 $processedCount 条数据（应该小于10000）")
        println("=== 取消机制测试完成 ===")
    }
    
    /**
     * 模拟数据处理的取消检查
     */
    fun simulateDataProcessingWithCancellation(
        totalData: Int,
        taskId: Long,
        getCurrentTaskId: () -> Long,
        onProgress: (Int) -> Unit = {}
    ): Boolean {
        var processed = 0
        
        for (i in 0 until totalData) {
            // 每100条数据检查一次取消状态
            if (i % 100 == 0) {
                if (getCurrentTaskId() != taskId) {
                    println("数据处理在第 $i 条时被取消 (taskId: $taskId, current: ${getCurrentTaskId()})")
                    return false
                }
            }
            
            processed++
            onProgress(processed)
            
            // 模拟处理时间
            Thread.sleep(1)
        }
        
        println("数据处理完成，共处理 $processed 条数据")
        return true
    }
}
