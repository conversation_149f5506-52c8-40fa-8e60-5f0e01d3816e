package com.lfb.android.footprint.utils

import android.content.Context
import android.net.Uri
import android.util.Log
import com.lfb.android.footprint.Manager.RealmModelManager
import com.lfb.android.footprint.model.StepDataRealmModel
import kotlinx.datetime.*
import java.io.BufferedReader
import java.io.InputStreamReader

/**
 * CSV数据解析器
 * 用于解析GPS轨迹数据的CSV文件并转换为StepDataRealmModel对象
 * 支持流式读取和批量处理，适用于大文件（百万级数据）
 */
class CsvDataParser(private val context: Context) {
    
    companion object {
        private const val TAG = "CsvDataParser"

        // CSV文件的预期列数
        private const val EXPECTED_COLUMNS = 11

        // 批量处理大小
        private const val BATCH_SIZE = 10000

        // CSV文件的列索引
        private const val COL_DATA_TIME = 0
        private const val COL_LOC_TYPE = 1
        private const val COL_LONGITUDE = 2
        private const val COL_LATITUDE = 3
        private const val COL_HEADING = 4
        private const val COL_ACCURACY = 5
        private const val COL_SPEED = 6
        private const val COL_DISTANCE = 7
        private const val COL_IS_BACK_FOREGROUND = 8
        private const val COL_STEP_TYPE = 9
        private const val COL_ALTITUDE = 10
    }
    
    /**
     * 进度回调接口
     */
    interface ProgressCallback {
        suspend fun onProgress(
            processedRows: Int,
            totalRows: Int,
            successRows: Int,
            errorRows: Int,
            duplicateRows: Int,
            currentBatch: Int,
            totalBatches: Int
        )

        suspend fun onBatchCompleted(batchNumber: Int, batchSize: Int)
    }

    /**
     * 解析结果数据类
     */
    data class ParseResult(
        val success: Boolean,
        val data: List<StepDataRealmModel> = emptyList(),
        val errorMessage: String? = null,
        val totalRows: Int = 0,
        val successRows: Int = 0,
        val errorRows: Int = 0,
        val duplicateRows: Int = 0
    )
    
    /**
     * 解析CSV文件（流式处理，适用于大文件）
     * @param uri 文件URI
     * @param progressCallback 进度回调
     * @return 解析结果
     */
    suspend fun parseCSVFileStreaming(uri: Uri, progressCallback: ProgressCallback? = null): ParseResult {
        return try {
            val inputStream = context.contentResolver.openInputStream(uri)
                ?: return ParseResult(false, errorMessage = "无法打开文件")

            val reader = BufferedReader(InputStreamReader(inputStream))

            // 读取并验证文件头
            val header = reader.readLine()
            if (header == null || !isValidHeader(header)) {
                reader.close()
                return ParseResult(false, errorMessage = "文件格式不正确，请确保是GPS轨迹数据的CSV文件")
            }

            // 预估总行数（通过文件大小估算）
            val fileSize = try {
                context.contentResolver.openFileDescriptor(uri, "r")?.use {
                    it.statSize
                } ?: 0L
            } catch (e: Exception) {
                0L
            }
            val estimatedRows = if (fileSize > 0) {
                // 更准确的估算：假设每行平均150字节
                maxOf((fileSize / 150).toInt() - 1, 0) // 减1是因为有表头行
            } else {
                0
            }

            // 流式处理数据
            val result = processDataStreaming(reader, estimatedRows, progressCallback)
            reader.close()

            result

        } catch (e: Exception) {
            Log.e(TAG, "解析CSV文件失败", e)
            ParseResult(false, errorMessage = "解析文件失败: ${e.message}")
        }
    }

    /**
     * 解析CSV文件（原有方法，保持兼容性）
     * @param uri 文件URI
     * @return 解析结果
     */
    suspend fun parseCSVFile(uri: Uri): ParseResult {
        return try {
            val inputStream = context.contentResolver.openInputStream(uri)
                ?: return ParseResult(false, errorMessage = "无法打开文件")
            
            val reader = BufferedReader(InputStreamReader(inputStream))
            val lines = reader.readLines()
            reader.close()
            
            if (lines.isEmpty()) {
                return ParseResult(false, errorMessage = "文件为空")
            }
            
            // 验证文件头
            val header = lines.first()
            if (!isValidHeader(header)) {
                return ParseResult(false, errorMessage = "文件格式不正确，请确保是GPS轨迹数据的CSV文件")
            }
            
            // 解析数据行
            val dataLines = lines.drop(1) // 跳过表头
            val results = mutableListOf<StepDataRealmModel>()
            var successCount = 0
            var errorCount = 0
            var duplicateCount = 0

            // 获取现有数据的时间戳集合，用于检查重复
            val realmManager = RealmModelManager.getInstance()
            val existingTimestamps = mutableSetOf<Long>()

            dataLines.forEachIndexed { index, line ->
                if (line.trim().isNotEmpty()) {
                    try {
                        val model = parseDataLine(line, index + 2) // +2 因为跳过了表头，且行号从1开始
                        if (model != null) {
                            // 检查是否已存在相同时间戳的数据
                            if (existingTimestamps.contains(model.dataTime)) {
                                duplicateCount++
                                Log.d(TAG, "跳过重复数据，时间戳: ${model.dataTime}")
                            } else {
                                // 检查数据库中是否已存在
                                val existingData = realmManager.queryModelByTime<StepDataRealmModel>(model.dataTime)
                                if (existingData != null) {
                                    duplicateCount++
                                    Log.d(TAG, "数据库中已存在相同时间戳的数据: ${model.dataTime}")
                                } else {
                                    results.add(model)
                                    existingTimestamps.add(model.dataTime)
                                    successCount++
                                }
                            }
                        } else {
                            errorCount++
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "解析第${index + 2}行数据失败: ${e.message}")
                        errorCount++
                    }
                }
            }
            
            if (results.isEmpty() && duplicateCount == 0) {
                return ParseResult(false, errorMessage = "没有有效的数据行")
            }

            ParseResult(
                success = true,
                data = results,
                totalRows = dataLines.size,
                successRows = successCount,
                errorRows = errorCount,
                duplicateRows = duplicateCount
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "解析CSV文件失败", e)
            ParseResult(false, errorMessage = "解析文件失败: ${e.message}")
        }
    }
    
    /**
     * 验证CSV文件头是否正确
     */
    private fun isValidHeader(header: String): Boolean {
        val expectedHeaders = listOf(
            "dataTime", "locType", "longitude", "latitude", "heading",
            "accuracy", "speed", "distance", "isBackForeground", "stepType", "altitude"
        )
        
        val actualHeaders = header.split(",").map { it.trim() }
        
        // 检查列数是否匹配
        if (actualHeaders.size != expectedHeaders.size) {
            Log.w(TAG, "列数不匹配，期望${expectedHeaders.size}列，实际${actualHeaders.size}列")
            return false
        }
        
        // 检查列名是否匹配（不区分大小写）
        expectedHeaders.forEachIndexed { index, expected ->
            if (actualHeaders.getOrNull(index)?.lowercase() != expected.lowercase()) {
                Log.w(TAG, "第${index + 1}列名称不匹配，期望'$expected'，实际'${actualHeaders.getOrNull(index)}'")
                return false
            }
        }
        
        return true
    }
    
    /**
     * 解析单行数据
     */
    private fun parseDataLine(line: String?, lineNumber: Int): StepDataRealmModel? {
        if (line.isNullOrBlank()) {
            Log.w(TAG, "第${lineNumber}行为空或null")
            return null
        }

        val parts = line.split(",")

        if (parts.size != EXPECTED_COLUMNS) {
            Log.w(TAG, "第${lineNumber}行列数不正确，期望${EXPECTED_COLUMNS}列，实际${parts.size}列")
            return null
        }
        
        return try {
            val dataTime = parts[COL_DATA_TIME].trim().toLong()
            val longitude = parts[COL_LONGITUDE].trim().toDouble()
            val latitude = parts[COL_LATITUDE].trim().toDouble()
            val heading = parts[COL_HEADING].trim().toDouble()
            val accuracy = parts[COL_ACCURACY].trim().toDouble()
            val speed = parts[COL_SPEED].trim().toDouble()
            val distance = parts[COL_DISTANCE].trim().toDouble()
            val altitude = parts[COL_ALTITUDE].trim().toDouble()
            
            // 验证基本数据有效性
            if (!isValidCoordinate(latitude, longitude)) {
                Log.w(TAG, "第${lineNumber}行坐标无效: lat=$latitude, lng=$longitude")
                return null
            }
            
            // 计算年份和日期信息
            val instant = Instant.fromEpochSeconds(dataTime)
            val dateTime = instant.toLocalDateTime(TimeZone.currentSystemDefault())
            val year = dateTime.year
            val dayOfYear = dateTime.dayOfYear
            val day = dateTime.dayOfMonth
            
            StepDataRealmModel().apply {
                this.dataTime = dataTime
                this.longitude = longitude
                this.latitude = latitude
                this.year = year
                this.day = day
                this.dayOfThisyear = dayOfYear
                this.heading = heading
                this.altitude = altitude
                this.hAccuracy = accuracy
                this.vAccuracy = accuracy // CSV中只有一个accuracy字段，同时赋值给水平和垂直精度
                this.speed = if (speed < 0) 0.0 else speed // 负值速度设为0
                this.distance = distance
            }
            
        } catch (e: NumberFormatException) {
            Log.w(TAG, "第${lineNumber}行数据格式错误: ${e.message}")
            null
        } catch (e: Exception) {
            Log.w(TAG, "第${lineNumber}行解析失败: ${e.message}")
            null
        }
    }
    
    /**
     * 验证坐标是否有效
     */
    private fun isValidCoordinate(latitude: Double, longitude: Double): Boolean {
        return latitude in -90.0..90.0 && longitude in -180.0..180.0
    }

    /**
     * 流式处理数据
     */
    private suspend fun processDataStreaming(
        reader: BufferedReader,
        estimatedRows: Int,
        progressCallback: ProgressCallback?
    ): ParseResult {
        val realmManager = RealmModelManager.getInstance()
        val existingTimestamps = mutableSetOf<Long>()

        var currentBatch = mutableListOf<StepDataRealmModel>()
        var totalProcessed = 0
        var successCount = 0
        var errorCount = 0
        var duplicateCount = 0
        var lineNumber = 2 // 从第2行开始（跳过表头）
        var batchNumber = 1

        val totalBatches = if (estimatedRows > 0) (estimatedRows / BATCH_SIZE) + 1 else 0

        // 记录初始内存使用情况
        val runtime = Runtime.getRuntime()
        val initialMemory = runtime.totalMemory() - runtime.freeMemory()
        Log.d(TAG, "开始流式处理，初始内存使用: ${initialMemory / 1024 / 1024}MB")

        // 初始进度回调
        progressCallback?.onProgress(0, estimatedRows, 0, 0, 0, 1, totalBatches)

        try {
            var line: String?
            while (reader.readLine().also { line = it } != null) {
                val currentLine = line
                if (!currentLine.isNullOrBlank()) {
                    totalProcessed++

                    try {
                        val model = parseDataLine(currentLine, lineNumber)
                        if (model != null) {
                            // 检查重复数据
                            if (existingTimestamps.contains(model.dataTime)) {
                                duplicateCount++
                                Log.d(TAG, "跳过重复数据，时间戳: ${model.dataTime}")
                            } else {
                                // 直接添加到批次中（跳过数据库重复检查以提高性能）
                                currentBatch.add(model)
                                existingTimestamps.add(model.dataTime)
                                successCount++
                            }
                        } else {
                            errorCount++
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "解析第${lineNumber}行数据失败: ${e.message}")
                        errorCount++
                    }

                    // 批量写入数据库
                    if (currentBatch.size >= BATCH_SIZE) {
                        realmManager.writeBatchToRealmOptimized(currentBatch)
                        progressCallback?.onBatchCompleted(batchNumber, currentBatch.size)

                        // 清空当前批次，释放内存
                        currentBatch.clear()
                        batchNumber++

                        // 清理已处理的时间戳集合，保持内存使用可控
                        if (existingTimestamps.size > BATCH_SIZE * 2) {
                            // 清理较旧的时间戳，保留最近的一批
                            val sortedTimestamps = existingTimestamps.sorted()
                            val recentTimestamps = sortedTimestamps.takeLast(BATCH_SIZE)
                            existingTimestamps.clear()
                            existingTimestamps.addAll(recentTimestamps)
                        }

                        // 批次完成后更新进度
                        progressCallback?.onProgress(
                            totalProcessed, estimatedRows, successCount,
                            errorCount, duplicateCount, batchNumber, totalBatches
                        )
                    } else {
                        // 每处理1000行就更新一次进度（但避免与批次完成重复）
                        if (totalProcessed % 1000 == 0) {
                            Log.d(TAG, "常规进度更新: 已处理 $totalProcessed 行，成功 $successCount 条")
                            progressCallback?.onProgress(
                                totalProcessed, estimatedRows, successCount,
                                errorCount, duplicateCount, batchNumber, totalBatches
                            )
                        }
                    }

                    // 给其他协程执行机会，避免阻塞UI
                    kotlinx.coroutines.yield()

                    // 建议垃圾回收（仅建议，不强制）
                    // if (batchNumber % 10 == 0) {
                    //     System.gc()
                    // }
                }

                lineNumber++
            }


            // 处理最后一批数据
            if (currentBatch.isNotEmpty()) {
                realmManager.writeBatchToRealmOptimized(currentBatch)
                progressCallback?.onBatchCompleted(batchNumber, currentBatch.size)
                currentBatch.clear()
            }

            // 最终进度更新
            progressCallback?.onProgress(
                totalProcessed, totalProcessed, successCount,
                errorCount, duplicateCount, batchNumber, batchNumber
            )

            // 记录最终内存使用情况
            val finalMemory = runtime.totalMemory() - runtime.freeMemory()
            Log.d(TAG, "流式处理完成，最终内存使用: ${finalMemory / 1024 / 1024}MB，" +
                    "内存增长: ${(finalMemory - initialMemory) / 1024 / 1024}MB")

            if (successCount == 0 && duplicateCount == 0) {
                return ParseResult(false, errorMessage = "没有有效的数据行")
            }

            return ParseResult(
                success = true,
                data = emptyList(), // 流式处理不返回数据列表
                totalRows = totalProcessed,
                successRows = successCount,
                errorRows = errorCount,
                duplicateRows = duplicateCount
            )

        } catch (e: Exception) {
            Log.e(TAG, "流式处理数据失败", e)
            return ParseResult(false, errorMessage = "处理数据失败: ${e.message}")
        }
    }
}
