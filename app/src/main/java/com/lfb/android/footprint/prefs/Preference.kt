// prefs/Preference.kt

package com.lfb.android.footprint.prefs

import android.content.Context
import android.content.SharedPreferences
import androidx.annotation.AnyThread
import androidx.core.content.edit
import prefs.PreferenceFlow
import java.io.*
import kotlin.properties.ReadWriteProperty
import kotlin.reflect.KProperty

class Preference<T>(
    private val context: Context,
    private val key: String,
    private val defaultValue: T,
    prefsName: String = "app_prefs"
) : ReadWriteProperty<Any, T> {

    private val prefs: SharedPreferences by lazy {
        context.applicationContext.getSharedPreferences(prefsName, Context.MODE_PRIVATE)
    }

    @Suppress("UNCHECKED_CAST")
    @AnyThread
    override fun getValue(thisRef: Any, property: KProperty<*>): T = with(prefs) {
        return when (defaultValue) {
            is Boolean -> getBoolean(key, defaultValue) as T
            is Int     -> getInt(key, defaultValue) as T
            is Long    -> getLong(key, defaultValue) as T
            is Float   -> getFloat(key, defaultValue) as T
            is Double  -> java.lang.Double.longBitsToDouble(
                getLong(key, java.lang.Double.doubleToRawLongBits(defaultValue))
            ) as T
            is String  -> getString(key, defaultValue) as T
            is Set<*>  -> getStringSet(key, defaultValue as Set<String>) as T
            else       -> deserialize(getString(key, null), defaultValue)
        }
    }

    @AnyThread
    override fun setValue(thisRef: Any, property: KProperty<*>, value: T) {
        prefs.edit(commit = false) {
            when (value) {
                is Boolean -> putBoolean(key, value)
                is Int     -> putInt(key, value)
                is Long    -> putLong(key, value)
                is Float   -> putFloat(key, value)
                is Double  -> putLong(key, java.lang.Double.doubleToRawLongBits(value))
                is String  -> putString(key, value)
                is Set<*>  -> putStringSet(key, value as Set<String>)
                else       -> putString(key, serialize(value))
            }
        }
        PreferenceFlow.notifyChanged(key, value)   // 发送流更新
    }

    /* -------- 序列化工具：任意 Serializable / Parcelable -------- */

    private fun serialize(obj: Any?): String? =
        if (obj == null) null
        else ByteArrayOutputStream().use { bos ->
            ObjectOutputStream(bos).use { it.writeObject(obj) }
            android.util.Base64.encodeToString(bos.toByteArray(), android.util.Base64.DEFAULT)
        }

    @Suppress("UNCHECKED_CAST")
    private fun <R> deserialize(str: String?, default: R): R =
        if (str == null) default
        else try {
            val data = android.util.Base64.decode(str, android.util.Base64.DEFAULT)
            ObjectInputStream(ByteArrayInputStream(data)).use { it.readObject() as R }
        } catch (e: Exception) { default }
}