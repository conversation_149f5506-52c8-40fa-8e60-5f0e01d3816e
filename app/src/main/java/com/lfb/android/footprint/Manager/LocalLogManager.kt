import android.content.Context

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileWriter
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

/**
 * 本地日志管理器
 * 功能：按日期记录日志，自动创建每日CSV日志文件
 * 日志保存在应用的外部文件目录/logs下
 */
class LocalLogManager private constructor(private val context: Context) {

    companion object {
        @Volatile
        private var INSTANCE: LocalLogManager? = null

        fun getInstance(context: Context): LocalLogManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: LocalLogManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }



    // 日期格式化器
    private val dateFormatter = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    private val timeFormatter = SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault())

    // 日志目录 - 使用应用的外部文件目录
    private val logDir: File by lazy {
        File(context.getExternalFilesDir(null), "logs").apply {
            if (!exists()) mkdirs()
        }
    }

    /**
     * 记录日志（异步版本）
     * @param message 日志内容
     */
    suspend fun log(message: String) {
        withContext(Dispatchers.IO) {
            writeLog(message)
        }
    }

    /**
     * 记录日志（同步版本）
     * @param message 日志内容
     */
    fun logSync(message: String) {
        writeLog(message)
    }

    /**
     * 实际写入日志的方法
     */
    private fun writeLog(message: String) {
        try {
            val currentTime = System.currentTimeMillis()
            val date = Date(currentTime)

            // 生成日志文件名
            val fileName = "log_${dateFormatter.format(date)}.csv"
            val logFile = File(logDir, fileName)

            // 格式化日志内容
            val timeStr = timeFormatter.format(date)
            val logEntry = "[$timeStr] $message\n"

            // 写入日志文件
            FileWriter(logFile, true).use { writer ->
                writer.write(logEntry)
                writer.flush()
            }

        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    /**
     * 获取指定日期的日志内容
     * @param date 日期
     * @return 日志内容，如果文件不存在返回null
     */
    suspend fun getLogByDate(date: Date): String? {
        return withContext(Dispatchers.IO) {
            try {
                val fileName = "log_${dateFormatter.format(date)}.csv"
                val logFile = File(logDir, fileName)

                if (logFile.exists()) {
                    logFile.readText()
                } else {
                    null
                }
            } catch (e: IOException) {
                e.printStackTrace()
                null
            }
        }
    }

    /**
     * 获取今天的日志内容
     */
    suspend fun getTodayLog(): String? {
        return getLogByDate(Date())
    }

    /**
     * 获取所有日志文件列表
     * @return 日志文件列表，按日期排序
     */
    suspend fun getAllLogFiles(): List<File> {
        return withContext(Dispatchers.IO) {
            logDir.listFiles { file ->
                file.name.startsWith("log_") && file.name.endsWith(".csv")
            }?.sortedByDescending { it.name } ?: emptyList()
        }
    }

    /**
     * 清理过期日志
     * @param daysToKeep 保留天数
     */
    suspend fun cleanOldLogs(daysToKeep: Int = 7) {
        withContext(Dispatchers.IO) {
            try {
                val cutoffTime = System.currentTimeMillis() - (daysToKeep * 24 * 60 * 60 * 1000L)

                logDir.listFiles()?.forEach { file ->
                    if (file.lastModified() < cutoffTime) {
                        file.delete()
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    /**
     * 获取日志目录大小
     * @return 目录大小（字节）
     */
    suspend fun getLogDirSize(): Long {
        return withContext(Dispatchers.IO) {
            try {
                logDir.walkTopDown()
                    .filter { it.isFile }
                    .map { it.length() }
                    .sum()
            } catch (e: Exception) {
                0L
            }
        }
    }
}

// 使用示例
/*
class MainActivity : AppCompatActivity() {
    private lateinit var logManager: LocalLogManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化日志管理器
        logManager = LocalLogManager.getInstance(this)

        // 使用协程记录日志（异步）
        lifecycleScope.launch {
            logManager.log("应用启动")
            logManager.log("用户点击了按钮")
            logManager.log("数据加载完成")

            // 获取今天的日志
            val todayLog = logManager.getTodayLog()
            println("今天的日志: $todayLog")

            // 清理7天前的日志
            logManager.cleanOldLogs(7)
        }

        // 或者直接同步记录日志
        logManager.logSync("同步记录的日志")
        logManager.logSync("LocationListener中使用这个方法")
    }
}
*/