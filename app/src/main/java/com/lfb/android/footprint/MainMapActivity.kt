package com.lfb.android.footprint

import android.os.Bundle
import androidx.activity.compose.setContent
import com.lfb.android.footprint.location.LocationManager
import com.lfb.android.footprint.location.LocationDataRecorder
import com.lfb.android.footprint.ui.components.mapScreen.MapScreen
import com.lfb.android.footprint.ui.theme.ThemeUtils

class MainMapActivity : BaseMapActivity() {

    private val locationManager by lazy { LocationManager.getInstance(this) }
    private val locationDataRecorder by lazy { LocationDataRecorder.getInstance(this) }

    override fun onCreate(savedInstanceState: Bundle?) {
        // 根据用户的地图主题配置设置Activity主题
        setThemeBasedOnMapConfig()

        super.onCreate(savedInstanceState)

        setContent {
            MapScreen(
                locationManager = locationManager,
                locationDataRecorder = locationDataRecorder,
                showRecordModeDialog = ::showRecordModeDialog
            )
        }
    }

    /**
     * 根据地图主题配置设置Activity主题
     * 避免启动时的白屏闪烁
     */
    private fun setThemeBasedOnMapConfig() {
        val themeResId = ThemeUtils.getActivityThemeForMapDisplayType(this)
        setTheme(themeResId)
    }
}