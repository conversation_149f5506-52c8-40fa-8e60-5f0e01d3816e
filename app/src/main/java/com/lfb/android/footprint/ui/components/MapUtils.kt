package com.lfb.android.footprint.ui.components

import com.mapbox.geojson.Point
import com.mapbox.maps.CameraOptions
import com.mapbox.maps.EdgeInsets
import com.mapbox.maps.MapView

import com.mapbox.maps.extension.compose.animation.viewport.MapViewportState
import com.mapbox.maps.plugin.animation.MapAnimationOptions
import com.mapbox.maps.plugin.animation.easeTo

/**
 * 地图相关的工具函数
 */

/**
 * 调整地图视角以适应轨迹并添加动画
 */
fun fitTrackAndAnimate(
    mapView: MapView,
    mapViewportState: MapViewportState,
    trackPoints: List<Point>
) {
    if (trackPoints.isEmpty()) return

    // 计算轨迹的边界框并调整地图视角
    if (trackPoints.size > 2) {
        mapView.getMapboxMap().cameraForCoordinates(
            trackPoints,
            CameraOptions.Builder().build(),
            EdgeInsets(100.0, 22.0, 100.0, 22.0), // 设置边距
            null, // maxZoom 可选
            null  // offset 可选
        ) { cameraOptions ->
            mapView.getMapboxMap().easeTo(
                cameraOptions,
                MapAnimationOptions.Builder()
                    .duration(300) // 动画时长，单位毫秒
                    .build()
            )
        }
    } else if (trackPoints.isNotEmpty()) {
        // 对于单点或两点，使用固定缩放级别和边距计算
        val newPoint = trackPoints.first()

        // 使用 Mapbox 的 easeTo 方法，配合 padding 参数
        mapView.getMapboxMap().easeTo(
            CameraOptions.Builder()
                .center(newPoint)
                .zoom(15.0)
                .padding(EdgeInsets(400.0, 100.0, 400.0, 200.0))
                .build(),
            MapAnimationOptions.Builder()
                .duration(300)
                .build()
        )
    }
}

/**
 * 调整地图视角以适应轨迹并添加边距
 */
fun fitTrackAndAnimateWithPadding(
    mapView: MapView,
    mapViewportState: MapViewportState,
    trackPoints: List<Point>,
    density: Float = 2.0f,
    topPaddingDp: Double = 200.0,
    leftPaddingDp: Double = 22.0,
    bottomPaddingDp: Double = 200.0,
    rightPaddingDp: Double = 22.0
) {
    // 将 dp 值转换为像素值
    val topPadding = topPaddingDp * density
    val leftPadding = leftPaddingDp * density
    val bottomPadding = bottomPaddingDp * density
    val rightPadding = rightPaddingDp * density

    // 计算轨迹的边界框并调整地图视角，支持自定义padding
    if (trackPoints.size > 2) {
        mapView.getMapboxMap().cameraForCoordinates(
            trackPoints,
            CameraOptions.Builder().build(),
            EdgeInsets(topPadding, leftPadding, bottomPadding, rightPadding), // 自定义边距
            null, // maxZoom 可选
            null  // offset 可选
        ) { cameraOptions ->
            mapView.getMapboxMap().easeTo(
                cameraOptions,
                MapAnimationOptions.Builder()
                    .duration(300) // 动画时长，单位毫秒
                    .build()
            )
        }
    } else if (trackPoints.isNotEmpty()) {
        // 对于单点或两点，使用固定缩放级别和边距计算
        val newPoint = trackPoints.first()

        // 使用 Mapbox 的 easeTo 方法，配合 padding 参数
        mapView.getMapboxMap().easeTo(
            CameraOptions.Builder()
                .center(newPoint)
                .zoom(15.0)
                .padding(EdgeInsets(topPadding, leftPadding, bottomPadding, rightPadding))
                .build(),
            MapAnimationOptions.Builder()
                .duration(300)
                .build()
        )
    }
}


