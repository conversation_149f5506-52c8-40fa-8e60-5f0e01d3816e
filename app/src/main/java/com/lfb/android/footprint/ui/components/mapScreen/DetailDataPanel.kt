package com.lfb.android.footprint.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.lfb.android.footprint.Manager.RealmModelManager
import com.lfb.android.footprint.model.StepDataRealmModel
import com.lfb.android.footprint.prefs.AppPrefs
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DetailDataPanel(
    modifier: Modifier = Modifier,
    onClose: () -> Unit,
    onDayDataChanged: (List<StepDataRealmModel>) -> Unit = {}, // 当天数据变化回调
    onDataPointSelected: (StepDataRealmModel) -> Unit = {}, // 数据点选择回调
    onHeightChanged: (Int) -> Unit = {}, // 面板高度变化回调
    onPreDeleteDataPoints: (List<StepDataRealmModel>) -> Unit = {}, // 预删除数据点回调
    onCancelPreDelete: () -> Unit = {}, // 取消预删除回调
    onConfirmDelete: (List<StepDataRealmModel>) -> Unit = {} // 确认删除回调
) {
    val coroutineScope = rememberCoroutineScope()
    val density = LocalDensity.current // 在Composable作用域内获取density

    // 状态管理
    var availableYears by remember { mutableStateOf<List<Int>>(emptyList()) }
    var availableDays by remember { mutableStateOf<List<Pair<Int, Int>>>(emptyList()) } // Pair<month, day>
    var dataPoints by remember { mutableStateOf<List<StepDataRealmModel>>(emptyList()) }

    var selectedYear by remember { mutableStateOf<Int?>(null) }
    var selectedMonth by remember { mutableStateOf<Int?>(null) }
    var selectedDay by remember { mutableStateOf<Int?>(null) }

    var isLoading by remember { mutableStateOf(true) }

    // 选择和删除状态管理
    var selectedDataPoints by remember { mutableStateOf<Set<Long>>(emptySet()) } // 使用dataTime作为唯一标识
    var isPreDeleteMode by remember { mutableStateOf(false) }
    var preDeleteDataPoints by remember { mutableStateOf<List<StepDataRealmModel>>(emptyList()) }

    // 编辑模式状态管理
    var isEditMode by remember { mutableStateOf(false) }

    // 单选模式下的选中数据点（用于地图显示）
    var singleSelectedDataPoint by remember { mutableStateOf<StepDataRealmModel?>(null) }

    // 初始化数据
    LaunchedEffect(Unit) {
        coroutineScope.launch {
            try {
                val realmManager = RealmModelManager.getInstance()
                availableYears = realmManager.getAvailableYears()

                // 获取最后一天的数据作为默认显示
                val lastDay = realmManager.getLastDataDay()
                lastDay?.let { (year, month, day) ->
                    selectedYear = year
                    selectedMonth = month
                    selectedDay = day

                    // 加载对应的数据
                    availableDays = realmManager.getAllDaysInYear(year)

                    dataPoints = realmManager.getDataPointsForDay(year, month, day)

                    onDayDataChanged(dataPoints) // 通知地图更新轨迹
                }
                isLoading = false
            } catch (e: Exception) {
                isLoading = false
                // 处理错误
            }
        }
    }

    ModalBottomSheet(
        onDismissRequest = onClose,
        modifier = modifier
            .fillMaxWidth()
            .fillMaxHeight(0.5f) // 占据屏幕高度的50%，让地图区域可见
            .onGloballyPositioned { coordinates ->
                // 获取面板实际高度并回调
                val heightInPx = coordinates.size.height
                val heightInDp = with(density) { heightInPx.toDp().value.toInt() }
                onHeightChanged(heightInDp)
            },
        shape = RoundedCornerShape(topStart = 22.dp, topEnd = 22.dp),
        containerColor = Color(0xFF000000),
        dragHandle = null
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(2.dp)
        ) {
            Spacer(modifier = Modifier.height(2.dp))
            // 顶部工具栏
            TopToolBar(
                isEditMode = isEditMode,
                onClose = onClose,
                onEditModeToggle = {
                    isEditMode = !isEditMode
                    // 切换编辑模式时重置选择状态
                    if (!isEditMode) {
                        selectedDataPoints = emptySet()
                        isPreDeleteMode = false
                        preDeleteDataPoints = emptyList()
                        singleSelectedDataPoint = null
                        onCancelPreDelete()
                    }
                }
            )
            
            Spacer(modifier = Modifier.height(2.dp))
            
            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(color = Color(0xFFE53935))
                }
            } else {
                Column(
                    modifier = Modifier.fillMaxSize()
                ) {
                    // 主要内容区域 - 三列布局
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f),
                        horizontalArrangement = Arrangement.spacedBy(1.dp)
                    ) {
                        // 年份列表 - 调小宽度
                        YearColumn(
                            years = availableYears,
                            selectedYear = selectedYear,
                            onYearSelected = { year ->
                                coroutineScope.launch {
                                    selectedYear = year
                                    selectedMonth = null
                                    selectedDay = null

                                    val realmManager = RealmModelManager.getInstance()
                                    availableDays = realmManager.getAllDaysInYear(year)
                                    dataPoints = emptyList()

                                    // 自动选择该年第一天有数据的日期
                                    val firstDay = realmManager.getFirstDataDayInYear(year)
                                    firstDay?.let { (_, month, day) ->
                                        selectedMonth = month
                                        selectedDay = day
                                        dataPoints = realmManager.getDataPointsForDay(year, month, day)
                                        onDayDataChanged(dataPoints) // 通知地图更新轨迹
                                    }
                                }
                            },
                            modifier = Modifier.weight(0.15f) // 从0.2f减少到0.15f
                        )

                        // 日期列表（包含月份信息）- 调小宽度
                        DateColumn(
                            year = selectedYear,
                            month = selectedMonth,
                            days = availableDays,
                            selectedDay = selectedDay,
                            onDaySelected = { monthDay ->
                                coroutineScope.launch {
                                    selectedMonth = monthDay.first
                                    selectedDay = monthDay.second

                                    selectedYear?.let { year ->
                                        val realmManager = RealmModelManager.getInstance()
                                        dataPoints = realmManager.getDataPointsForDay(year, monthDay.first, monthDay.second)
                                        onDayDataChanged(dataPoints) // 通知地图更新轨迹
                                    }
                                }
                            },
                            modifier = Modifier.weight(0.2f) // 从0.25f减少到0.2f
                        )

                        // 数据点列表 - 增加宽度
                        DataPointColumn(
                            dataPoints = dataPoints,
                            selectedDataPoints = selectedDataPoints,
                            singleSelectedDataPoint = singleSelectedDataPoint,
                            isEditMode = isEditMode,
                            isPreDeleteMode = isPreDeleteMode,
                            onDataPointClick = { dataPoint ->
                                if (!isEditMode && !isPreDeleteMode) {
                                    // 单选模式：设置单选数据点并调用回调
                                    singleSelectedDataPoint = dataPoint
                                    onDataPointSelected(dataPoint)
                                    // 在单选模式下，也将选中的点加入到selectedDataPoints中，以便进行删除操作
                                    selectedDataPoints = setOf(dataPoint.dataTime)
                                }
                            },
                            onDataPointSelectionChanged = { dataTime, isSelected ->
                                if (isEditMode || isPreDeleteMode) {
                                    // 多选模式：更新选中集合
                                    selectedDataPoints = if (isSelected) {
                                        selectedDataPoints + dataTime
                                    } else {
                                        selectedDataPoints - dataTime
                                    }

                                    // 如果在预删除模式下，需要实时更新预删除数据和地图显示
                                    if (isPreDeleteMode) {
                                        preDeleteDataPoints = dataPoints.filter { it.dataTime in selectedDataPoints }
                                        val deleteDataTimes = preDeleteDataPoints.map { it.dataTime }.toSet()
                                        val remainingDataPoints = dataPoints.filter { dataPoint ->
                                            !deleteDataTimes.contains(dataPoint.dataTime)
                                        }
                                        onPreDeleteDataPoints(preDeleteDataPoints)
                                    }
                                }
                            },
                            modifier = Modifier.weight(0.65f) // 从0.55f增加到0.65f
                        )
                    }

                    // 底部工具栏
                    BottomToolBar(
                        dataPoints = dataPoints,
                        selectedDataPoints = selectedDataPoints,
                        isEditMode = isEditMode,
                        isPreDeleteMode = isPreDeleteMode,
                        onSelectAll = {
                            // 全选功能只在编辑模式或预删除模式下可用
                            if (isEditMode || isPreDeleteMode) {
                                selectedDataPoints = if (selectedDataPoints.size == dataPoints.size && dataPoints.isNotEmpty()) {
                                    emptySet() // 如果已经全选，则取消全选
                                } else {
                                    dataPoints.map { it.dataTime }.toSet() // 否则全选
                                }

                                // 如果在预删除模式下，需要更新预删除数据和地图显示
                                if (isPreDeleteMode) {
                                    preDeleteDataPoints = dataPoints.filter { it.dataTime in selectedDataPoints }
                                    val deleteDataTimes = preDeleteDataPoints.map { it.dataTime }.toSet()
                                    val remainingDataPoints = dataPoints.filter { dataPoint ->
                                        !deleteDataTimes.contains(dataPoint.dataTime)
                                    }
                                    onPreDeleteDataPoints(preDeleteDataPoints)
                                }
                            }
                        },
                        onPreDelete = {
                            // 预删除功能在有选中数据点时可用（无论是否在编辑模式）
                            if (selectedDataPoints.isNotEmpty()) {
                                isPreDeleteMode = true
                                preDeleteDataPoints = dataPoints.filter { it.dataTime in selectedDataPoints }
                                // 进入预删除模式时，立即更新地图显示剩余轨迹
                                val deleteDataTimes = preDeleteDataPoints.map { it.dataTime }.toSet()
                                val remainingDataPoints = dataPoints.filter { dataPoint ->
                                    !deleteDataTimes.contains(dataPoint.dataTime)
                                }
                                onPreDeleteDataPoints(preDeleteDataPoints)
                            }
                        },
                        onCancelPreDelete = {
                            isPreDeleteMode = false
                            preDeleteDataPoints = emptyList()
                            if (isEditMode) {
                                // 编辑模式下取消预删除：清空所有选择
                                selectedDataPoints = emptySet()
                            } else {
                                // 单选模式下取消预删除：保持单选状态
                                singleSelectedDataPoint?.let { selectedPoint ->
                                    selectedDataPoints = setOf(selectedPoint.dataTime)
                                }
                            }
                            onCancelPreDelete()
                        },
                        onConfirmDelete = {
                            coroutineScope.launch {
                                try {
                                    val realmManager = RealmModelManager.getInstance()
                                    val success = realmManager.deleteDataPoints(preDeleteDataPoints.map { it.dataTime })

                                    if (success) {
                                        // 删除成功，检查当天是否还有数据
                                        selectedYear?.let { year ->
                                            selectedMonth?.let { month ->
                                                selectedDay?.let { day ->
                                                    val remainingData = realmManager.getDataPointsForDay(year, month, day)

                                                    if (remainingData.isNotEmpty()) {
                                                        // 当天还有数据，显示剩余数据
                                                        dataPoints = remainingData
                                                        onDayDataChanged(dataPoints)
                                                    } else {
                                                        // 当天没有数据了，显示下一天的数据
                                                        val nextDay = realmManager.getNextDataDay(year, month, day)
                                                        if (nextDay != null) {
                                                            selectedYear = nextDay.first
                                                            selectedMonth = nextDay.second
                                                            selectedDay = nextDay.third

                                                            // 更新可用日期列表
                                                            availableDays = realmManager.getAllDaysInYear(nextDay.first)
                                                            dataPoints = realmManager.getDataPointsForDay(nextDay.first, nextDay.second, nextDay.third)
                                                            onDayDataChanged(dataPoints)
                                                        } else {
                                                            // 没有下一天数据，清空显示
                                                            dataPoints = emptyList()
                                                            onDayDataChanged(dataPoints)
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        // 重置状态，离开预删除模式
                                        isPreDeleteMode = false
                                        preDeleteDataPoints = emptyList()
                                        selectedDataPoints = emptySet()
                                        onConfirmDelete(preDeleteDataPoints)
                                    }
                                } catch (e: Exception) {
                                    // 处理删除失败的情况
                                    println("Delete failed: ${e.message}")
                                }
                            }
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun TopToolBar(
    isEditMode: Boolean = false,
    onClose: () -> Unit,
    onEditModeToggle: () -> Unit = {}
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 32.dp, vertical = 12.dp), // 从12dp减少到8dp
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "选择时间",
            color = Color.White,
            fontSize = 16.sp, // 修复字体大小
            fontWeight = FontWeight.Bold
        )

        Row(
            horizontalArrangement = Arrangement.spacedBy(12.dp), // 从16dp减少到12dp
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 导出按钮
            Text(
                text = "导出",
                color = Color(0xFFE53935),
                fontSize = 13.sp, // 从14sp减少到13sp
                modifier = Modifier.clickable {
                    // TODO: 实现导出功能
                }
            )

            // 编辑按钮
            Text(
                text = if (isEditMode) "完成" else "编辑",
                color = Color(0xFFE53935),
                fontSize = 13.sp, // 从14sp减少到13sp
                fontWeight = if (isEditMode) FontWeight.Bold else FontWeight.Normal,
                modifier = Modifier.clickable {
                    onEditModeToggle()
                }
            )

            // 关闭按钮
            Text(
                text = "关闭",
                color = Color(0xFFE53935),
                fontSize = 13.sp, // 从14sp减少到13sp
                modifier = Modifier.clickable { onClose() }
            )
        }
    }
}

@Composable
fun BottomToolBar(
    dataPoints: List<StepDataRealmModel> = emptyList(),
    selectedDataPoints: Set<Long> = emptySet(),
    isEditMode: Boolean = false,
    isPreDeleteMode: Boolean = false,
    onSelectAll: () -> Unit = {},
    onPreDelete: () -> Unit = {},
    onCancelPreDelete: () -> Unit = {},
    onConfirmDelete: () -> Unit = {}
) {
    // 始终显示底部工具栏
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color(0xFF1A1A1A))
                .padding(horizontal = 16.dp, vertical = 12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (!isPreDeleteMode) {
                // 正常模式：根据是否在编辑模式显示不同的按钮
                if (isEditMode) {
                    // 编辑模式：显示全选和预删除按钮
                    Text(
                        text = if (selectedDataPoints.size == dataPoints.size && dataPoints.isNotEmpty()) "取消全选" else "全选",
                        color = Color(0xFFE53935),
                        fontSize = 14.sp,
                        modifier = Modifier.clickable {
                            onSelectAll()
                        }
                    )

                    Text(
                        text = "预删除 (${selectedDataPoints.size})",
                        color = if (selectedDataPoints.isNotEmpty()) Color(0xFFE53935) else Color.Gray,
                        fontSize = 14.sp,
                        modifier = Modifier.clickable {
                            if (selectedDataPoints.isNotEmpty()) {
                                onPreDelete()
                            }
                        }
                    )
                } else {
                    // 单选模式：只显示预删除按钮
                    Spacer(modifier = Modifier.weight(1f))

                    Text(
                        text = "预删除 (${selectedDataPoints.size})",
                        color = if (selectedDataPoints.isNotEmpty()) Color(0xFFE53935) else Color.Gray,
                        fontSize = 14.sp,
                        modifier = Modifier.clickable {
                            if (selectedDataPoints.isNotEmpty()) {
                                onPreDelete()
                            }
                        }
                    )
                }
            } else {
                // 预删除模式：根据是否在编辑模式显示不同的按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    if (isEditMode) {
                        // 编辑模式下的预删除：显示全选按钮
                        Text(
                            text = if (selectedDataPoints.size == dataPoints.size && dataPoints.isNotEmpty()) "取消全选" else "全选",
                            color = Color(0xFFE53935),
                            fontSize = 14.sp,
                            modifier = Modifier.clickable {
                                onSelectAll()
                            }
                        )
                    } else {
                        // 单选模式下的预删除：不显示全选按钮
                        Spacer(modifier = Modifier.width(1.dp))
                    }

                    Row(
                        horizontalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        Text(
                            text = "取消预删除",
                            color = Color(0xFFE53935),
                            fontSize = 14.sp,
                            modifier = Modifier.clickable {
                                onCancelPreDelete()
                            }
                        )

                        Text(
                            text = "确认删除 (${selectedDataPoints.size})",
                            color = Color(0xFFFF5722),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.clickable {
                                if (selectedDataPoints.isNotEmpty()) {
                                    onConfirmDelete()
                                }
                            }
                        )
                    }
                }
            }
        }
}

@Composable
fun YearColumn(
    years: List<Int>,
    selectedYear: Int?,
    onYearSelected: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxHeight()
            .background(Color(0xFF1A1A1A))
    ) {
        Text(
            text = "年份",
            color = Color.White.copy(alpha = 0.6f),
            fontSize = 10.sp,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 6.dp),
            textAlign = TextAlign.Center
        )

        LazyColumn {
            items(years) { year ->
                YearItem(
                    year = year,
                    isSelected = year == selectedYear,
                    onClick = { onYearSelected(year) }
                )
            }
        }
    }
}

@Composable
fun YearItem(
    year: Int,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = if (isSelected) Color(0xFFE53935) else Color.Transparent
            )
            .clickable { onClick() }
            .padding(vertical = 8.dp, horizontal = 4.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "$year",
            color = if (isSelected) Color.White else Color.White.copy(alpha = 0.8f),
            fontSize = 12.sp,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
        )
    }
}

@Composable
fun DateColumn(
    year: Int?,
    month: Int?,
    days: List<Pair<Int, Int>>, // Pair<month, day>
    selectedDay: Int?,
    onDaySelected: (Pair<Int, Int>) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxHeight()
            .background(Color(0xFF2A2A2A))
    ) {
        Text(
            text = "选择时间",
            color = Color.White.copy(alpha = 0.6f),
            fontSize = 10.sp,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 6.dp),
            textAlign = TextAlign.Center
        )

        LazyColumn {
            items(days) { monthDay ->
                DateItem(
                    year = year,
                    monthDay = monthDay,
                    dataCount = 0, // 不再使用这个参数，DateItem内部异步加载
                    isSelected = monthDay.first == month && monthDay.second == selectedDay,
                    onClick = { onDaySelected(monthDay) }
                )
            }
        }
    }
}

@Composable
fun DateItem(
    year: Int?,
    monthDay: Pair<Int, Int>,
    dataCount: Int,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val coroutineScope = rememberCoroutineScope()
    var actualDataCount by remember(monthDay) { mutableStateOf<Int?>(null) }
    var isLoading by remember(monthDay) { mutableStateOf(false) }

    // 异步加载轨迹点数量
    LaunchedEffect(monthDay, year) {
        if (year != null && actualDataCount == null && !isLoading) {
            isLoading = true
            coroutineScope.launch {
                try {
                    val realmManager = RealmModelManager.getInstance()
                    val count = realmManager.getDataPointCountForDay(year, monthDay.first, monthDay.second)
                    actualDataCount = count
                } catch (e: Exception) {
                    actualDataCount = 0
                } finally {
                    isLoading = false
                }
            }
        }
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = if (isSelected) Color(0xFFE53935) else Color.Transparent
            )
            .clickable { onClick() }
            .padding(vertical = 6.dp, horizontal = 4.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "${String.format("%02d", monthDay.first)}月${String.format("%02d", monthDay.second)}日",
                color = if (isSelected) Color.White else Color.White.copy(alpha = 0.8f),
                fontSize = 10.sp,
                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
            )
            // 显示轨迹点数量（异步加载）
            Text(
                text = when {
                    isLoading -> "轨迹点数: ..."
                    actualDataCount != null -> "轨迹点数: $actualDataCount"
                    else -> "轨迹点数: ..."
                },
                color = if (isSelected) Color.White.copy(alpha = 0.8f) else Color.White.copy(alpha = 0.5f),
                fontSize = 8.sp
            )
        }
    }
}

@Composable
fun DataPointColumn(
    dataPoints: List<StepDataRealmModel>,
    selectedDataPoints: Set<Long> = emptySet(),
    singleSelectedDataPoint: StepDataRealmModel? = null,
    isEditMode: Boolean = false,
    isPreDeleteMode: Boolean = false,
    onDataPointClick: (StepDataRealmModel) -> Unit = {},
    onDataPointSelectionChanged: (Long, Boolean) -> Unit = { _, _ -> },
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxHeight()
            .background(Color(0xFF4A4A4A))
    ) {
        // 数据点数量显示
        Text(
            text = "数据点: ${dataPoints.size}",
            color = Color.White.copy(alpha = 0.8f),
            fontSize = 12.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(8.dp)
        )

        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(1.dp)
        ) {
            itemsIndexed(
                items = dataPoints,
                key = { _, dataPoint -> dataPoint.dataTime } // 添加稳定的key以提高性能
            ) { index, dataPoint ->
                DataPointItem(
                    dataPoint = dataPoint,
                    index = index + 1,
                    isSelected = selectedDataPoints.contains(dataPoint.dataTime),
                    isSingleSelected = singleSelectedDataPoint?.dataTime == dataPoint.dataTime,
                    isEditMode = isEditMode,
                    isPreDeleteMode = isPreDeleteMode,
                    onClick = { onDataPointClick(dataPoint) },
                    onSelectionChanged = { isSelected ->
                        onDataPointSelectionChanged(dataPoint.dataTime, isSelected)
                    }
                )
            }
        }
    }
}

// 在文件顶部创建共享的格式化器、Shape对象和颜色对象
private val timeFormatter = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
private val checkboxShape = androidx.compose.foundation.shape.RoundedCornerShape(2.dp)

// 预定义颜色对象，避免重复创建
private val selectedBackgroundColor = Color(0x33FF5722)
private val singleSelectedBackgroundColor = Color(0x33E53935)
private val checkboxColor = Color(0xFFE53935)
private val whiteColor80 = Color.White.copy(alpha = 0.8f)
private val whiteColor90 = Color.White.copy(alpha = 0.9f)
private val whiteColor70 = Color.White.copy(alpha = 0.7f)
private val whiteColor60 = Color.White.copy(alpha = 0.6f)

@Composable
fun DataPointItem(
    dataPoint: StepDataRealmModel,
    index: Int,
    isSelected: Boolean = false,
    isSingleSelected: Boolean = false,
    isEditMode: Boolean = false,
    isPreDeleteMode: Boolean = false,
    onClick: () -> Unit = {},
    onSelectionChanged: (Boolean) -> Unit = {}
) {
    // 缓存格式化的字符串，避免重复计算
    val formattedTime = remember(dataPoint.dataTime) {
        timeFormatter.format(Date(dataPoint.dataTime * 1000))
    }

    val formattedCoordinates = remember(dataPoint.latitude, dataPoint.longitude) {
        "(${String.format("%.4f,%.4f", dataPoint.latitude, dataPoint.longitude)})"
    }

    val formattedDetails = remember(dataPoint.speed, dataPoint.altitude, dataPoint.hAccuracy) {
        "速度:${String.format("%.1f", dataPoint.speed)} 海拔:${String.format("%.2f", dataPoint.altitude)} 定位精度:${String.format("%.2f", dataPoint.hAccuracy)}"
    }

    // 缓存点击处理逻辑
    val clickHandler = remember(isPreDeleteMode, isEditMode, isSelected) {
        {
            when {
                isPreDeleteMode -> onSelectionChanged(!isSelected)
                isEditMode -> onSelectionChanged(!isSelected)
                else -> onClick()
            }
        }
    }

    // 缓存背景颜色
    val backgroundColor = remember(isSelected, isSingleSelected, isEditMode, isPreDeleteMode) {
        when {
            isSelected && (isEditMode || isPreDeleteMode) -> selectedBackgroundColor
            isSingleSelected && !isEditMode && !isPreDeleteMode -> singleSelectedBackgroundColor
            else -> Color.Transparent
        }
    }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { clickHandler() }
            .background(backgroundColor)
            .padding(horizontal = 8.dp, vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 左侧选择框（只在编辑模式或预删除模式下显示）
        if (isEditMode || isPreDeleteMode) {
            Box(
                modifier = Modifier
                    .size(16.dp)
                    .clickable {
                        if (isEditMode || isPreDeleteMode) {
                            onSelectionChanged(!isSelected)
                        }
                    }
                    .background(
                        if (isSelected) checkboxColor else Color.Transparent,
                        shape = checkboxShape
                    )
                    .border(
                        1.dp,
                        checkboxColor,
                        shape = checkboxShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                if (isSelected) {
                    Text(
                        text = "✓",
                        color = Color.White,
                        fontSize = 10.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        } else {
            // 单选模式下不显示选择框，用空白占位
            Spacer(modifier = Modifier.size(16.dp))
        }

        Spacer(modifier = Modifier.width(8.dp))

        // 主要内容
        Column(
            modifier = Modifier.weight(1f)
        ) {
            // 缓存文本颜色
            val titleColor = remember(isSelected, isSingleSelected, isEditMode, isPreDeleteMode) {
                when {
                    isSelected && (isEditMode || isPreDeleteMode) -> whiteColor80
                    isSingleSelected && !isEditMode && !isPreDeleteMode -> whiteColor90
                    else -> Color.White
                }
            }

            val detailColor = remember(isSelected, isSingleSelected, isEditMode, isPreDeleteMode) {
                when {
                    isSelected && (isEditMode || isPreDeleteMode) -> whiteColor60
                    isSingleSelected && !isEditMode && !isPreDeleteMode -> whiteColor80
                    else -> whiteColor70
                }
            }

            val fontWeight = remember(isSingleSelected, isEditMode, isPreDeleteMode) {
                if (isSingleSelected && !isEditMode && !isPreDeleteMode) FontWeight.Medium else FontWeight.Normal
            }

            // 第一行：时间(坐标)
            Text(
                text = "$formattedTime $formattedCoordinates",
                color = titleColor,
                fontSize = 11.sp,
                fontWeight = fontWeight
            )

            // 第二行：速度，海拔，定位精度
            Text(
                text = formattedDetails,
                color = detailColor,
                fontSize = 10.sp
            )
        }
    }
}
