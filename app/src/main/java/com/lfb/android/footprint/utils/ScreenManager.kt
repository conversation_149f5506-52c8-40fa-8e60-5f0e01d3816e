package com.lfb.android.footprint.utils

import android.app.Activity
import android.content.Context
import android.os.PowerManager
import android.view.WindowManager

/**
 * 屏幕管理工具类
 * 用于管理屏幕常亮状态，防止应用在前台时屏幕熄屏
 */
object ScreenManager {
    
    private var wakeLock: PowerManager.WakeLock? = null
    private var isKeepScreenOnEnabled = false
    
    /**
     * 启用屏幕常亮
     * @param activity Activity实例
     */
    fun enableKeepScreenOn(activity: Activity) {
        try {
            // 方法1：使用Window FLAG_KEEP_SCREEN_ON（推荐）
            activity.window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            isKeepScreenOnEnabled = true
            
            // 方法2：使用WakeLock作为备用方案
            if (wakeLock == null) {
                val powerManager = activity.getSystemService(Context.POWER_SERVICE) as PowerManager
                wakeLock = powerManager.newWakeLock(
                    PowerManager.SCREEN_DIM_WAKE_LOCK or PowerManager.ACQUIRE_CAUSES_WAKEUP,
                    "FootPrint:ScreenWakeLock"
                )
                wakeLock?.setReferenceCounted(false)
            }
            
            if (wakeLock?.isHeld == false) {
                wakeLock?.acquire(10 * 60 * 1000L) // 10分钟超时，防止意外长时间持有
            }
            
            println("ScreenManager: 屏幕常亮已启用")
        } catch (e: Exception) {
            println("ScreenManager: 启用屏幕常亮失败: ${e.message}")
        }
    }
    
    /**
     * 禁用屏幕常亮
     * @param activity Activity实例
     */
    fun disableKeepScreenOn(activity: Activity) {
        try {
            // 移除Window FLAG_KEEP_SCREEN_ON
            activity.window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            isKeepScreenOnEnabled = false
            
            // 释放WakeLock
            if (wakeLock?.isHeld == true) {
                wakeLock?.release()
            }
            
            println("ScreenManager: 屏幕常亮已禁用")
        } catch (e: Exception) {
            println("ScreenManager: 禁用屏幕常亮失败: ${e.message}")
        }
    }
    
    /**
     * 检查屏幕常亮是否已启用
     * @return true如果已启用，false如果未启用
     */
    fun isKeepScreenOnEnabled(): Boolean {
        return isKeepScreenOnEnabled
    }
    
    /**
     * 释放所有资源
     * 应在应用退出时调用
     */
    fun release() {
        try {
            if (wakeLock?.isHeld == true) {
                wakeLock?.release()
            }
            wakeLock = null
            isKeepScreenOnEnabled = false
            println("ScreenManager: 资源已释放")
        } catch (e: Exception) {
            println("ScreenManager: 释放资源失败: ${e.message}")
        }
    }
}
