<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/Theme.FootPrint"
        tools:targetApi="31">
        <activity
            android:name=".GuideActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.FootPrint">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".MainMapActivity"
            android:exported="false"
            android:label="@string/app_name"
            android:theme="@style/Theme.FootPrint">
        </activity>
        <activity
            android:name=".SettingsActivity"
            android:exported="false"
            android:label="设置"
            android:theme="@style/Theme.FootPrint" />
        <activity
            android:name=".ImportExportActivity"
            android:exported="false"
            android:label="导入导出"
            android:theme="@style/Theme.FootPrint" />
        <activity
            android:name=".LocationModeActivity"
            android:exported="false"
            android:label="定位模式"
            android:theme="@style/Theme.FootPrint" />
        <service
            android:name=".service.LocationService"
            android:foregroundServiceType="location" />
    </application>

    <!-- Include this permission to grab user's general location -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <!-- Include only if your app benefits from precise location access. -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <!-- Add background location permission -->
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!-- 屏幕唤醒权限，用于保持屏幕常亮 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
</manifest>