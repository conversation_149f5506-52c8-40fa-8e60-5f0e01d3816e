<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <!-- 基础主题 - 深色背景，避免启动白屏 -->
    <style name="Theme.FootPrint" parent="android:Theme.Material.NoActionBar">
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>

        <!-- 设置窗口背景为深色，避免启动时白屏闪烁 -->
        <item name="android:windowBackground">@color/app_startup_background</item>

        <!-- 对于 API 27+，可以更好地控制导航栏分隔线等 -->
        <item name="android:enforceStatusBarContrast" tools:targetApi="q">false</item>
        <item name="android:enforceNavigationBarContrast" tools:targetApi="q">false</item>

    </style>

    <!-- 浅色主题变体 -->
    <style name="Theme.FootPrint.Light" parent="android:Theme.Material.Light.NoActionBar">
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>

        <!-- 设置窗口背景为浅色 -->
        <item name="android:windowBackground">@color/app_startup_background_light</item>

        <!-- 对于 API 27+，可以更好地控制导航栏分隔线等 -->
        <item name="android:enforceStatusBarContrast" tools:targetApi="q">false</item>
        <item name="android:enforceNavigationBarContrast" tools:targetApi="q">false</item>

    </style>

<!--    <style name="Theme.FootPrint" parent="Theme.Material3.DayNight.NoActionBar">-->

<!--        <item name="android:windowTranslucentStatus">true</item>-->
<!--        <item name="android:windowDrawsSystemBarBackgrounds">true</item>-->
<!--        <item name="android:statusBarColor">@android:color/transparent</item>-->
<!--        <item name="android:windowLightStatusBar">true</item>-->
<!--        <item name="android:windowBackground">@android:color/transparent</item>-->
<!--    </style>-->

</resources>