package com.lfb.android.footprint

import kotlinx.coroutines.*
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.Assert.*

/**
 * 测试取消机制的简单示例
 */
class CancellationTest {

    @Test
    fun testCancellationMechanism() = runTest {
        var processedCount = 0
        var cancelled = false
        
        // 模拟数据处理任务
        val job = launch {
            try {
                repeat(10000) { i ->
                    // 检查是否被取消
                    if (cancelled || currentCoroutineContext[Job]!!.isCancelled) {
                        println("Task cancelled at iteration $i")
                        return@launch
                    }
                    
                    processedCount++
                    
                    // 模拟一些处理时间
                    delay(1)
                }
            } catch (e: CancellationException) {
                println("Task cancelled via exception")
                throw e
            }
        }
        
        // 让任务运行一小段时间
        delay(50)
        
        // 设置取消标志
        cancelled = true
        
        // 取消任务
        job.cancel()
        
        // 等待任务完成
        job.join()
        
        // 验证任务确实被取消了
        assertTrue("Task should have been cancelled", processedCount < 10000)
        println("Processed $processedCount items before cancellation")
    }
    
    @Test
    fun testTaskIdMechanism() = runTest {
        var currentTaskId = 0L
        var processedCount = 0
        
        // 启动第一个任务
        val taskId1 = ++currentTaskId
        val job1 = launch {
            repeat(1000) { i ->
                // 检查任务ID是否仍然有效
                if (currentTaskId != taskId1) {
                    println("Task 1 invalidated at iteration $i")
                    return@launch
                }
                processedCount++
                delay(1)
            }
        }
        
        // 让第一个任务运行一小段时间
        delay(50)
        
        // 启动第二个任务（这会使第一个任务的ID失效）
        val taskId2 = ++currentTaskId
        val job2 = launch {
            repeat(100) { i ->
                if (currentTaskId != taskId2) {
                    println("Task 2 invalidated at iteration $i")
                    return@launch
                }
                delay(1)
            }
        }
        
        // 等待两个任务完成
        joinAll(job1, job2)
        
        // 验证第一个任务被正确中断
        assertTrue("First task should have been interrupted", processedCount < 1000)
        println("Task 1 processed $processedCount items before being invalidated")
    }
}
